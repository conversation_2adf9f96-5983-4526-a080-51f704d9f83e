import Text from "@/components/text";
import View from "@/components/view";
import Button from "@/components/button";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import { validationForm } from "./validationForm";
import { FormTypeProps } from "@/interfaces/dashboard";
import { toast } from "@/utils/custom-hooks/use-toast";

import { useNavigate, useParams } from "react-router-dom";
import useForm from "@/utils/custom-hooks/use-form";
import Input from "@/components/input";
// import { dietStatusOptions } from "./dietFormOptions";
import DepartmentType from "../departmentType/DepartmentType";
import BouncingLoader from "@/components/BouncingLoader";
import { Proctoscopy } from "@/interfaces/proctoscopy";
import { useProctoscopy } from "@/actions/calls/proctoscopy";
import { clearProctoscopyDetailSlice } from "@/actions/slices/proctoscopy";

const ProctoscopyForm: React.FC<FormTypeProps> = ({
  formType = "add",
  onModalSuccess,
}) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { addProctoscopyHandler, editProctoscopyHandler, proctoscopyDetailHandler, cleanUp } =
    useProctoscopy();
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const proctoscopyData = useSelector((state: any) => state.proctoscopy.proctoscopyDetailData);
  const { values, handleChange, onSetHandler } = useForm<Proctoscopy | null>(proctoscopyData);

  console.log("proctoscopyData", proctoscopyData);
  

  useEffect(() => {
    if (formType === "edit" && id) {
      proctoscopyDetailHandler(id, () => {}, [], (status) => {
        setIsLoading(status === "pending" ? true : status === "failed" ? true : status === "success" && false);
      }
      );
    }
    return () => {
      cleanUp();
      dispatch(clearProctoscopyDetailSlice());
    };
  }, [id, formType]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    let proctoscopyFormObj: Partial<Proctoscopy> = {};

    try {
      for (let [key, value] of formData.entries()) {
        proctoscopyFormObj[key as keyof Proctoscopy] = value as any;
      }
      await validationForm.validate(proctoscopyFormObj, { abortEarly: false });
      setErrors({});
      setIsSubmitting(true);
      if (formType === "add") {
        addProctoscopyHandler(proctoscopyFormObj, (success: boolean) => {
          if (success) {
            toast({
              title: "Success!",
              description: "Proctoscopy Added successfully.",
              variant: "success",
            });
            if (onModalSuccess) {
              return onModalSuccess();
            }
            navigate(-1);
          } else {
            setIsSubmitting(false);
            toast({
              title: "Error!",
              description: "Failed to add Proctoscopy",
              variant: "destructive",
            });
          }
        });
      } else if (id) {
        editProctoscopyHandler(id, proctoscopyFormObj, (success: boolean) => {
          if (success) {
            toast({
              title: "Success!",
              description: "Proctoscopy Updated successfully.",
              variant: "success",
            });
            if (onModalSuccess) {
              return onModalSuccess();
            }
            navigate(-1);
          } else {
            setIsSubmitting(false);
            toast({
              title: "Error!",
              description: "Failed to update Proctoscopy",
              variant: "destructive",
            });
          }
          setIsSubmitting(false);
        });
      }
    } catch (error: any) {
      setIsSubmitting(false);
      if (error.inner) {
        const validationErrors: Record<string, string> = {};
        error.inner.forEach((e: any) => {
          validationErrors[e.path] = e.message;
        });
        setErrors(validationErrors);
      }
    }
  };

  return (
    <View className="min-h-screen dark:bg-background flex flex-col  items-center p-4">
      <BouncingLoader isLoading={isLoading} />
      <View className="border border-border bg-white dark:bg-card rounded-lg shadow-card w-full max-w-4xl p-6 md:p-8 mb-8">
        <View className=" flex items-center justify-between">
          <Text
            as="h2"
            weight="font-bold"
            className="text-2xl font-bold text-center text-primary mb-2"
          >
            Proctoscopy Record Entry
          </Text>
          {!onModalSuccess && (
            <Button onPress={() => navigate(-1)} variant="outline">
              Back to Home
            </Button>
          )}
        </View>
        <Text as="p" className="text-text-light text-left mb-6">
          {/* {formType === "add" && "Fill in the details to create a new account"} */}
          Fill in the Proctoscopy details
        </Text>
          <form onSubmit={handleSubmit}>
          <View className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <View>
              <Input
                required={true}
                id="proctoscopys_name"
                name="proctoscopys_name"
                label="Proctoscopy Name"
                onChange={handleChange}
                // options={dres}
                error={errors?.proctoscopys_name}
                value={values?.proctoscopys_name || ""}
                placeholder="Enter Proctoscopy Name"
              />
            </View>
            <View>
              <DepartmentType
                required={true}
                value={values?.department_type || ""}
                error={errors?.department_type}
                onChange={(value) => onSetHandler("department_type", value)}
              />
            </View>
          </View>
           
 
          <View className="col-span-2 mt-6">
            <Button
              htmlType="submit"
              loading={isSubmitting}
              className="w-full bg-primary text-white rounded-md py-3 font-medium hover:bg-primary-600 transition focus:outline-none focus:ring-2 focus:ring-primary-300 focus:ring-offset-2"
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>
          </View>
        </form>
      </View>
    </View>
  );
};

export default ProctoscopyForm;
