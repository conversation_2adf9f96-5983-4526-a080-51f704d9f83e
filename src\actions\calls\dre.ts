import LaunchApi from "../api";
import { useDispatch } from "react-redux";
import { ApiCallback } from "@/interfaces/api";
import { AuthPayload } from "@/interfaces/slices/auth";
import {
  DRE_ADD_URL,
  DRE_DELETE_URL,
  DRE_DETAILS_URL,
  DRE_DROPDOWN_URL,
  DRE_EDIT_URL,
  DRE_LIST_URL,
} from "@/utils/urls/backend";
import {
  dreDetailSlice,
  dreDropdownSlice,
  dreListSlice,
} from "../slices/dre";
import { LoadingStatus } from "@/interfaces";
// import { GENERIC_ERROR_MESSAGE } from "@/utils/message";

const api = new LaunchApi();

export const useDre = () => {
  const dispatch = useDispatch();

  const addDreHandler = async <T>(
    data: T,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.post(
        DRE_ADD_URL,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            // dispatch(addPatientSlice());
            callback(true, response.data);
          } else {
            callback(false);
          }
        },
        data
      );
    } catch (error) {
      callback(false);
    }
  };

  const dreListHandler = async (
    page: number | string = 1,
    callback: ApiCallback,
    search?: string | null,
    sort_by?: string | null,
    sort_order?: string | null,
    data?: any,
    isLoading?: (status: LoadingStatus) => void
  ): Promise<void> => {
    try {
      await api.get(
        `${DRE_LIST_URL}?page=${page}${search ? "&search=" + search : ""}${
          sort_by ? "&sort_by=" + sort_by : ""
        }${sort_order ? "&sort_order=" + sort_order : ""}`,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(dreListSlice(response.data));
            return callback(true);
          } else {
            return callback(false);
          }
        },
        data,
        (status) => {
          isLoading?.(status);
        }
      );
    } catch (error) {
      callback(false);
    }
  };

  const dreDetailHandler = async (
    id: string,
    callback: ApiCallback,
    data?: any,
    isLoading?: (status: LoadingStatus) => void
  ): Promise<void> => {
    try {
      await api.get(
        DRE_DETAILS_URL + "/" + id,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(dreDetailSlice(response.data));
            return callback(true);
          } else {
            return callback(false);
          }
        },
        data,
        (status) => {
          isLoading?.(status);
        }
      );
    } catch (error) {
      callback(false);
    }
  };

  const editDreHandler = async (
    id: string,
    data: any,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.put(
        DRE_EDIT_URL + "/" + id,
        (_: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            return callback(true);
          } else {
            return callback(false);
          }
        },
        data
      );
    } catch (error) {
      callback(false);
    }
  };

  const deleteDreHandler = async (
    id: string,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.delete(
        DRE_DELETE_URL,
        id,
        (_: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            return callback(true);
          } else {
            return callback(false);
          }
        }
      );
    } catch (error) {
      callback(false);
    }
  };

  const dreDropdownHandler = async (
    callback: ApiCallback,
    departmentValue?: string
  ): Promise<void> => {
    try {
      await api.get(
        DRE_DROPDOWN_URL + "/" + departmentValue ||  "All",
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            console.log(response.data, "dreDropdownData");
            dispatch(dreDropdownSlice(response.data));
            return callback(true);
          } else {
            return callback(false);
          }
        },
      );
    } catch (error) {
      callback(false);
    }
  };

  const cleanUp = () => {
    api.cleanup();
  };

  return {
    cleanUp,
    addDreHandler,
    dreListHandler,
    dreDetailHandler,
    editDreHandler,
    deleteDreHandler,
    dreDropdownHandler,
  };
};
