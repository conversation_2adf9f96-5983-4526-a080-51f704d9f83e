import View from "@/components/view";
import Text from "@/components/text";
import Button from "@/components/button";
import React, { useEffect, useState } from "react";
import {
  ArrowLeft,
  DollarSign,
  Activity,
  Package,
  Building,
  FileText,
  Clipboard,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { useServiceCost } from "@/actions/calls/serviceCost";
import { clearserviceCostDetailSlice } from "@/actions/slices/serviceCost";
import BouncingLoader from "@/components/BouncingLoader";
import InfoCard from "@/components/ui/infoCard";

const ServiceCostDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);

  const { serviceCostDetailHandler, cleanUp } = useServiceCost();
  const serviceData = useSelector(
    (state: any) => state.serviceCost.serviceCostDetailData
  );

  useEffect(() => {
    if (id) {
      serviceCostDetailHandler(
        id,
        (_: boolean) => {},
        [],
        (status) => {
          setIsLoading(
            status === "pending"
              ? true
              : status === "failed"
              ? true
              : status === "success" && false
          );
        }
      );
    }
  }, [id]);

  useEffect(() => {
    return () => {
      cleanUp();
      dispatch(clearserviceCostDetailSlice());
    };
  }, [dispatch]);

  return (
    <React.Fragment>
      <BouncingLoader isLoading={isLoading} />
      <View className="space-y-6 container mx-auto py-8">
        {/* Header Section */}
        <View className="flex justify-between items-center mb-6">
          <View>
            <Text
              as="h1"
              weight="font-semibold"
              className="text-2xl md:text-3xl font-bold"
            >
              Service Cost Details
            </Text>
            <Text as="p" className="text-muted-foreground">
              View detailed information about the service cost
            </Text>
          </View>
          <View className="flex gap-3">
            <Button
              variant="outline"
              size="small"
              className="flex items-center gap-2"
              onClick={() => navigate(-1)}
            >
              {/* <Link  className="flex items-center gap-2"> */}
              <ArrowLeft size={16} />
              Back to Service Costs
              {/* </Link> */}
            </Button>
          </View>
        </View>

        <div className="container mx-auto px-6 py-8">
          {/* Service Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <InfoCard
              label="Service Cost"
              value={`$${serviceData?.cost || "N/A"}`}
              icon={<DollarSign className="h-6 w-6" />}
              className="!bg-card dark:!bg-card border border-border"
              valueStyle="text-green-700 dark:text-green-400"
              iconStyle="bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400"
            />

            <InfoCard
              label="Status"
              value={serviceData?.status || "N/A"}
              icon={<Activity className="h-6 w-6" />}
              className="!bg-card border border-border"
              valueStyle="text-blue-700 dark:text-blue-400"
              iconStyle="bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400"
            />

            <InfoCard
              label="Case Type"
              value={serviceData?.case_type || "N/A"}
              icon={<Clipboard className="h-6 w-6" />}
              className="!bg-card border border-border"
              valueStyle="text-purple-700 dark:text-purple-400"
              iconStyle="bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400"
            />
          </div>

          {/* Detailed Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Service Information */}
            <Card className="bg-gradient-to-br from-card to-card/50 border-border/50 shadow-lg">
              <CardContent className="p-8">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-3 rounded-full bg-primary/10 text-primary">
                    <Package className="h-6 w-6" />
                  </div>
                  <h2 className="text-2xl font-semibold">
                    Service Information
                  </h2>
                </div>

                <div className="space-y-6">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-muted-foreground">
                      Service Name
                    </label>
                    <p className="text-lg font-medium">
                      {serviceData?.service_name || "N/A"}
                    </p>
                  </div>

                  <div className="space-y-2 flex flex-col  gap-2">
                    <label className="text-sm font-medium text-muted-foreground">
                      Department
                    </label>
                    <Text as="p" className="flex items-center ">
                      <Building className="h-4 w-4 mr-2" />
                      {serviceData?.department_type || "N/A"}
                    </Text>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Description */}
            <Card className="bg-gradient-to-br from-card to-card/50 border-border/50 shadow-lg">
              <CardContent className="p-8">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-3 rounded-full bg-secondary/10 text-secondary-foreground">
                    <FileText className="h-6 w-6" />
                  </div>
                  <h2 className="text-2xl font-semibold">Description</h2>
                </div>

                <p className="text-muted-foreground leading-relaxed">
                  {serviceData?.description || "N/A"}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </View>
    </React.Fragment>
  );
};

export default ServiceCostDetail;
