:root{
    --primary-color:"";
    --secondary-color:"";
    --tertiary-color:"";

    --bg-primary-color:"";
    --bg-secondary-color:"";
    --bg-tertiary-color:"";

    --text-primary-color:"";
    --text-secondary-color:"";

    --button-radius:8px;
    --input-radius:8px;
}

[data-theme='dark']{
    --primary-color:#000;
}

[type='button'],button{
    border: none;
    outline: none;
    color:var(--primary-color);
    border-radius: var(--button-radius);
}

input{
    border: none;
    outline: none;
    border-radius: var(--input-radius) ;
    padding: 5px;
}

.centered-content{
    width: 100%;
    height: 100%;
    display:flex;
    align-items: center;
    justify-content: center;
    border-radius: 100%;
}