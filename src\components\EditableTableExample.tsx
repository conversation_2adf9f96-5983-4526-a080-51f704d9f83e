import React, { useState } from 'react';
import EditableTable, { ColumnConfig } from './EditableTable';

// Example component demonstrating the enhanced EditableTable functionality
const EditableTableExample: React.FC = () => {
  // Example data for backward compatibility test
  const [basicTableData, setBasicTableData] = useState([
    ['1', '<PERSON>', '<EMAIL>', '30'],
    ['2', '<PERSON>', '<EMAIL>', '25'],
  ]);

  const basicTableHeaders = ['ID', 'Name', 'Email', 'Age'];

  // Example data for enhanced functionality
  const [enhancedTableData, setEnhancedTableData] = useState([
    ['1', '<PERSON>', '<EMAIL>', '2023-12-01', true, 'Manager', 'Experienced team lead'],
    ['2', '<PERSON>', '<EMAIL>', '2023-11-15', false, 'Developer', 'Frontend specialist'],
  ]);

  const enhancedTableHeaders = ['ID', 'Name', 'Email', 'Join Date', 'Active', 'Role', 'Notes'];

  // Column configurations for enhanced table
  const columnConfigs: ColumnConfig[] = [
    {
      header: 'ID',
      type: 'number',
      placeholder: 'Enter ID',
      required: true,
    },
    {
      header: 'Name',
      type: 'text',
      placeholder: 'Enter full name',
      required: true,
      validation: (value) => {
        if (value && value.length < 2) {
          return 'Name must be at least 2 characters long';
        }
        return null;
      },
    },
    {
      header: 'Email',
      type: 'email',
      placeholder: 'Enter email address',
      required: true,
    },
    {
      header: 'Join Date',
      type: 'date',
      placeholder: 'Select join date',
      required: true,
    },
    {
      header: 'Active',
      type: 'checkbox',
    },
    {
      header: 'Role',
      type: 'select',
      placeholder: 'Select role',
      required: true,
      options: [
        { value: 'Manager', label: 'Manager' },
        { value: 'Developer', label: 'Developer' },
        { value: 'Designer', label: 'Designer' },
        { value: 'Analyst', label: 'Analyst' },
      ],
    },
    {
      header: 'Notes',
      type: 'textarea',
      placeholder: 'Enter additional notes',
    },
  ];

  const handleBasicCellEdit = (rowIndex: number, colIndex: number, value: any) => {
    const newData = [...basicTableData];
    newData[rowIndex][colIndex - 1] = value;
    setBasicTableData(newData);
  };

  const handleBasicRowAdd = (newRow: any[]) => {
    setBasicTableData([...basicTableData, newRow]);
  };

  const handleBasicRowDelete = (rowIndex: number) => {
    const newData = basicTableData.filter((_, index) => index !== rowIndex);
    setBasicTableData(newData);
  };

  const handleEnhancedCellEdit = (rowIndex: number, colIndex: number, value: any) => {
    const newData = [...enhancedTableData];
    newData[rowIndex][colIndex - 1] = value;
    setEnhancedTableData(newData);
  };

  const handleEnhancedRowAdd = (newRow: any[]) => {
    setEnhancedTableData([...enhancedTableData, newRow]);
  };

  const handleEnhancedRowDelete = (rowIndex: number) => {
    const newData = enhancedTableData.filter((_, index) => index !== rowIndex);
    setEnhancedTableData(newData);
  };

  return (
    <div className="p-6 space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-4">Basic EditableTable (Backward Compatibility)</h2>
        <p className="text-gray-600 mb-4">
          This table uses the original API without columnConfigs - should work exactly as before.
        </p>
        <EditableTable
          tableData={basicTableData}
          tableHeaders={basicTableHeaders}
          onCellEdit={handleBasicCellEdit}
          onRowAdd={handleBasicRowAdd}
          onRowDelete={handleBasicRowDelete}
          editable={true}
          addRowEnabled={true}
          deleteRowEnabled={true}
        />
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">Enhanced EditableTable (New Features)</h2>
        <p className="text-gray-600 mb-4">
          This table uses columnConfigs to enable different input types: text, email, date, checkbox, select, and textarea.
        </p>
        <EditableTable
          tableData={enhancedTableData}
          tableHeaders={enhancedTableHeaders}
          columnConfigs={columnConfigs}
          onCellEdit={handleEnhancedCellEdit}
          onRowAdd={handleEnhancedRowAdd}
          onRowDelete={handleEnhancedRowDelete}
          editable={true}
          addRowEnabled={true}
          deleteRowEnabled={true}
        />
      </div>
    </div>
  );
};

export default EditableTableExample;
