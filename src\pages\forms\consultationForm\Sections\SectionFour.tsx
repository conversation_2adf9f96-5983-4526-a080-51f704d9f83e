import React, { useEffect } from "react";
import Input from "@/components/input";
import View from "@/components/view";
// import { Appointment } from "@/interfaces/appointments";
import useForm from "@/utils/custom-hooks/use-form";
// import Textarea from "@/components/Textarea";
// import SearchSelect from "@/components/SearchSelect";
// import { useOpd } from "@/actions/calls/opd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/actions/store";
// import { useEffect } from "react";
// import dayjs from "dayjs";
import TipTapTextEditor from "@/components/TipTapTexteditor";
import { Consultation } from "@/interfaces/consultation";
import TransferList from "@/components/TransferList";
import { Card } from "@/components/ui/card";
import Text from "@/components/text";
import { useMedicine } from "@/actions/calls/medicine";
import MedicinesSection from "@/components/MedicinesSection";
import { useDiet } from "@/actions/calls/diet";
import { setDietPlanModel } from "@/actions/slices/medicalStatus";
import { useYogaAsana } from "@/actions/calls/yogaAsana";
import { useFoodAdvice } from "@/actions/calls/foodAdvice";
// import Text from "@/components/text";
// import Button from "@/components/button";

interface SectionFourProps {
  // errorsTemperature: string;
  // errorsBp: string;
  // errorsPulse: string;
  // errorsCvs: string;
  // errorsRs: string;
  // errorsTest: string;
  // postExaminationData: any;
  mainOnSetHandler: (name: string, value: any) => void;
}

const SectionFour: React.FC<SectionFourProps> = ({
  // errorsTemperature,
  // errorsBp,
  // errorsPulse,
  // errorsCvs,
  // errorsRs,
  // errorsTest,
  // postExaminationData,
  // mainOnSetHandler,
}) => {
  // const examinationDetails = useSelector(
  //   (state: RootState) => state.examinations.examinationDetails
  // );
  // const { values, handleChange } = useForm<Examination | null>(
  //   examinationDetails
  // );
  const { medicineDropdownHandler } = useMedicine();
  const { dietDropdownHandler } = useDiet();
  const { yogaAsanaDropdownHandler } = useYogaAsana();
  const { foodAdviceDropdownHandler } = useFoodAdvice();

  const consultationDetail = useSelector(
    (state: RootState) => state.consultation.consultationDetailData
  );

  const consultationDetailData = {
    ...consultationDetail?.consultations,
    ...consultationDetail?.proctologyOrNonProctology,
  };

  const dietDropdownData = useSelector(
    (state: RootState) => state.diet.dietDropdownData
  );

  const medicineDropdownData = useSelector(
    (state: RootState) => state.medicines.medicineDropdownData
  )?.map((item: any) => ({
    id: item?.id,
    label: item?.medicine_name,
    value: item?.medicine_name,
  }));
  const dietDropdownObj = dietDropdownData?.map((item: any) => ({
    id: item?.id,
    label: item?.diet_name + " ( " + item?.description + " )",
    value: item?.id ,
    description: item?.description || "",
  }));
  const yogaAsanaDropdownData = useSelector(
    (state: RootState) => state.yogaAsana.yogaAsanaDropdownData
  )?.map((item: any) => ({
    id: item?.id,
    label: item?.asana_name,
    value: item?.id + " ( " + item?.description + " )",
    description: item?.description || "",
  }));

  const foodAdviceDropdownData = useSelector(
    (state: RootState) => state.foodAdvice.foodAdviceDropdownList
  )?.map((item: any) => ({
    id: item?.id,
    label: item?.advice_text,
    value: item?.id + " ( " + item?.description + " )",
    description: item?.description || "",
  }));
  const consultationData = useSelector(
    (state: any) => state?.consultation?.consultationDetailData
  );

  //consultationData?.consultations?.type

  // useEffect(() => {
  //   medicineDropdownHandler(() => {});
  //   dietDropdownHandler(() => {});
  // }, []);

  // const testIds = testData?.split(",")?.map((item: any) => item.trim());
  // const testLabelMap = testObj?.filter((item: any) =>
  //   testIds?.includes(item?.value?.toString())
  // )?.map((item: any) => {
  //   return {
  //     id: item?.value,
  //     label: item?.label,
  //     value: item?.value,
  //   };
  // });
  // const testLabelMap = testObj?.filter((item: any) =>
  //   testIds?.includes(item?.value?.toString())
  // )?.map((item: any) => item?.label)?.join(",");
  // console.log("testLabelMap", testLabelMap);

  useEffect(() => {
    if (consultationData?.consultations?.type) {
      medicineDropdownHandler(
        () => {},
        ["id","unit_price","medicine_name"],
        consultationData?.consultations?.type
      );
      dietDropdownHandler(() => {},
      consultationData?.consultations?.type);
      
      if (consultationData?.consultations?.type === "Non Proctology") {
        foodAdviceDropdownHandler(() => {},
        consultationData?.consultations?.type);
        yogaAsanaDropdownHandler(() => {},
        consultationData?.consultations?.type);
      }
    }
  }, [consultationData?.consultations?.type]);

  // const testIds = testData?.split(",")?.map((item: any) => item.trim());
  // const testLabelMap = testObj?.filter((item: any) =>
  //   testIds?.includes(item?.value?.toString())
  // )?.map((item: any) => {
  //   return {
  //     id: item?.value,
  //     label: item?.label,
  //     value: item?.value,
  //   };
  // });
  // const testLabelMap = testObj?.filter((item: any) =>
  //   testIds?.includes(item?.value?.toString())
  // )?.map((item: any) => item?.label)?.join(",");
  // console.log("testLabelMap", testLabelMap);

  const { values, handleChange, handleTipTapChange, onSetHandler } =
    useForm<Consultation | null>(consultationDetailData);

  const dispatch = useDispatch();
  console.log("medicines", consultationDetailData?.medicines);
  console.log("medicineDropdownData", medicineDropdownData);
  

  return (
    <React.Fragment>
      <View>
        {/* Plan  */}
        <TipTapTextEditor
          name="treatment_plan"
          value={values?.treatment_plan || ""}
          onChange={handleTipTapChange}
          label="Treatment Plan"
          placeholder="Enter plan..."
        />
      </View>

      <Card className="mt-2 shadow-none border-none ">
        <Text className="text-lg font-bold pb-2 mb-2">Estimated Cost</Text>
        <View className="grid grid-cols-1 md:grid-cols-2 gap-6 bg-neutral-100 dark:bg-background rounded-lg p-4">
          <View>
            <Input
              id="amount"
              name="amount"
              label="Amount"
              onChange={handleChange}
              value={values?.amount ? values?.amount + "" : ""}
              placeholder="Enter Amount"
              className="bg-card"
            />
          </View>
          <View>
            <Input
              id="surgical_cost"
              name="surgical_cost"
              label="Surgical Cost"
              onChange={handleChange}
              value={values?.surgical_cost ? values?.surgical_cost + "" : ""}
              placeholder="Enter Surgical Cost"
              className="bg-card"
            />
          </View>
          
          {/* <View>
            <SingleSelector
              id="currency"
              label="Currency"
              name="currency"
              value={values?.currency || ""}
              placeholder="Select Currency"
              onChange={(value) => {
                onSetHandler("currency", value);
              }}
              options={[
                { label: "INR", value: "INR" },
                { label: "USD", value: "USD" },
                { label: "EUR", value: "EUR" },
              ]}
              className="bg-card"
            />
          </View> */}
        </View>
      </Card>

      {/* Diat plan  */}
      <View>
        <TransferList
          name="diet_plan"
          label="Diet Plan"
          sourceData={dietDropdownObj}
          selectedItems={
            values?.diet_plan
              ? Array.isArray(values?.diet_plan)
                ? values?.diet_plan
                : JSON.parse(values?.diet_plan)
              : []
          }
          onSelectionChange={(value) => {
            onSetHandler("diet_plan", value);
          }}
          onAllowCustomValues={() => {
            dispatch(setDietPlanModel(true));
          }}
          customValuePlaceholder="Add custom data"
          placeholder="Search diat plan..."
          sourceTitle="Available Diat Plan"
          selectedTitle="Selected Diat Plan"
          height="150px"
          searchable
          showCount
          allowSelectAll
          // allowCustomValues
          // customValuePlaceholder="Add custom diat plan"
        />
      </View>

      {consultationDetailData?.type === "Non Proctology" && (
        <>
          {/* Yoga advice  */}
          <View>
            <TransferList
              name="yoga_asana"
              label="Yoga Advice"
              sourceData={yogaAsanaDropdownData}
              selectedItems={
                values?.yoga_asana
                  ? Array.isArray(values?.yoga_asana)
                    ? values?.yoga_asana
                    : JSON.parse(values?.yoga_asana)
                  : []
              }
              onSelectionChange={(value) => {
                onSetHandler("yoga_asana", value);
              }}
              placeholder="Search yoga advice..."
              sourceTitle="Available Yoga Advice"
              selectedTitle="Selected Yoga Advice"
              height="150px"
              searchable
              showCount
              allowSelectAll
              // allowCustomValues
              // customValuePlaceholder="Add custom yoga advice"
            />
          </View>

          {/* food advice  */}
          <View>
            <TransferList
              name="food_advice"
              label="Food Advice"
              sourceData={foodAdviceDropdownData}
              selectedItems={
                values?.food_advice
                  ? Array.isArray(values?.food_advice)
                    ? values?.food_advice
                    : JSON.parse(values?.food_advice)
                  : []
              }
              onSelectionChange={(value) => {
                onSetHandler("food_advice", value);
              }}
              placeholder="Search food advice..."
              sourceTitle="Available Food Advice"
              selectedTitle="Selected Food Advice"
              height="150px"
              searchable
              showCount
              allowSelectAll
              // allowCustomValues
              // customValuePlaceholder="Add custom yoga advice"
            />
          </View>
        </>
      )}

      {/* Medicines  */}
      <View className=" rounded-lg">
        <MedicinesSection
          // errorsDosage={errorsDosage}
          // errorsTiming={errorsTiming}
          // errorsMedicines={errorsMedicines}
          medicinesList={medicineDropdownData}
          medicineData={consultationDetailData?.medicines}
          // medicineData={postExaminationData?.medicines}
          onSetHandler={onSetHandler}
        />
      </View>
      <View>
        <TipTapTextEditor
          name="advice"
          value={values?.advice || ""}
          onChange={handleTipTapChange}
          label="Advice"
          placeholder="Give advice to patient..."
        />
      </View>
    </React.Fragment>
  );
};
export default SectionFour;
