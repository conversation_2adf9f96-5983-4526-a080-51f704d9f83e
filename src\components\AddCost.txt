import View from "./view";
import Button from "./button";
import { Input } from "./ui/input";
import { Plus, X } from "lucide-react";
import SingleSelector from "./SingleSelector";
import { Card, CardHeader, CardTitle } from "./ui/card";
import React, { useEffect, useState } from "react";
import useForm from "@/utils/custom-hooks/use-form";

interface InputField {
  name: string;
  label: string;
  type: string;
  placeholder?: string;
  required?: boolean;
}

interface SelectDropDown {
  name: string;
  label: string;
  required?: boolean;
  placeholder?: string;
  options?: { label: string; value: string }[];
}

interface AddCostInputProps {
  title: string;
  minItems?: number;
  maxItems?: number;
  className?: string;
  defaultValue?: string[];
  cardClassName?: string;
  addButtonText?: string;
  inputField: InputField;
  selectDropDown: SelectDropDown;
}

interface InputItem {
  id: string;
  input: InputField;
  selectDropDown: SelectDropDown;
}

const AddCostInput: React.FC<AddCostInputProps> = ({
  title,
  minItems = 5,
  maxItems = 5,
  className = "",
  cardClassName = "",
  addButtonText,
  inputField,
  defaultValue,
  selectDropDown,
}) => {
  const [inputs, setInputs] = useState<InputItem[]>([]);

  const [serviceCost, setServiceCost] = useState<string>("");

  // const { values, onSetHandler } = useForm<any>({
  //   input: inputField,
  //   selectDropDown: selectDropDown,
  //   id: ``,
  // });

  const handleAdd = () => {
    setInputs([
      ...inputs,
      {
        input: inputField,
        selectDropDown: selectDropDown,
        id: `${Date.now()}_${Math.random()}`,
      },
    ]);
  };

  useEffect(() => {
    if (defaultValue && defaultValue.length > 0) {
      const serviceCostString =
        defaultValue
          .map((item) => {
            const [service, cost] = item.split("#");
            return `${service}#${cost}`;
          })
          .join(",") + ",";

      setServiceCost(serviceCostString);
      console.log(defaultValue);
      const newInputs = defaultValue.map(() => ({
        input: inputField,
        selectDropDown: selectDropDown,
        id: `${Date.now()}_${Math.random()}`,
      }));

      setInputs(newInputs);
    }
  }, [defaultValue]);

  const handleRemove = (id: string) => {
    const filtered = inputs.filter((item) => item.id !== id);
    setInputs(filtered);
  };

  const onGetValue = (service: string, cost: string) => {
    setServiceCost((prev) => {
      if (!prev) return `${service}#${cost}`;
      return `${prev},${service}#${cost}`;
    });
  };

  return (
    <Card
      className={`${className} ${cardClassName}`}
      style={{ backgroundColor: "var(--background)" }}
    >
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <input type="text" hidden value={serviceCost} name="Service" />
      {inputs.map((item: any, index: number) => (
        <InputsComp
          item={item}
          key={item.id}
          index={index}
          onGetValue={onGetValue}
          handleRemove={handleRemove}
        />
      ))}

      <Button
        type="button"
        variant="primary"
        onClick={handleAdd}
        disabled={inputs.length >= maxItems}
        className="mt-4 flex items-center w-full justify-center"
      >
        <Plus className="h-4 w-4 mr-2" />
        {addButtonText}
      </Button>
    </Card>
  );
};

const InputsComp = ({ item, index, handleRemove, onGetValue }: any) => {
  const [cost, setCost] = useState("");
  const [service, setService] = useState("");
  return (
    <React.Fragment>
      <View className="p-6 space-y-4">
        <View>
          <View className="flex justify-end">
            <Button
              type="button"
              variant="danger"
              className="px-2 py-1"
              // disabled={inputs.length <= minItems}
              onClick={() => handleRemove(item.id)}
            >
              <X className="h-4 w-4" />
            </Button>
          </View>

          <View className="flex justify-between gap-2">
            {/* Select dropdown */}
            <View className="w-full">
              <SingleSelector
                label={item.selectDropDown.label}
                id={`${item.selectDropDown.name}_${index}`}
                options={item.selectDropDown.options || []}
                placeholder={
                  item.selectDropDown.placeholder ||
                  `Select ${item.selectDropDown.label}`
                }
                // name={item.selectDropDown.name}
                // label={item.selectDropDown.label}
                // id={`${item.selectDropDown.name}_${index}`}
                // options={item.selectDropDown.options || []}
                // name={`${item.selectDropDown.name}_${index}`}
                // onChange={(value) => {
                // Handle onChange logic here if needed
                value={service}
                onChange={(value) => {
                  // onSetHandler(
                  //   `${item.selectDropDown.name}_${index}`,
                  //   value.split("#")[0]
                  // );
                  // onSetHandler(
                  //   `${item.input.name}_${index}`,
                  //   value.split("#")[1]
                  // );
                  setCost(value.split("#")[1]);
                  setService(value.split("#")[0]);
                  onGetValue(value.split("#")[0], value.split("#")[1]);
                }}
              />
            </View>

            {/* Input field */}
            <View className="w-full">
              <label className="block text-sm font-medium mb-1">
                {item.input.label}
                {item.input.required && (
                  <span className="text-red-500 ml-1">*</span>
                )}
              </label>
              <Input
                value={cost}
                readOnly={true}
                type={item.input.type}
                placeholder={item.input.placeholder}
                // type={item.input.type}
                // name={`${item.input.name}_${index}`}
                // value={values[`${item.input.name}_${index}`] || ""}
                // placeholder={item.input.placeholder}
                // onChange={(e) => {
                //   // Handle input value change here if needed
                // }}
              />
            </View>
          </View>
        </View>
      </View>
    </React.Fragment>
  );
};

export default AddCostInput;
