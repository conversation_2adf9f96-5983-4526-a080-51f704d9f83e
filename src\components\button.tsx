import {
  BtnVariant,
  ButtonProps,
  SetButtonSizeProps,
} from "@/interfaces/components/button";

const setVariantCssHandler: Partial<Record<BtnVariant | "default", string>> = {
  default: "bg-primary hover:bg-primary-600",
  ghost: "bg-transparent outline-none focus:outline-none",
  danger: "bg-danger text-white focus:outline-none outline-none hover:bg-red-800",
  primary: "bg-primary outline-none hover:bg-primary-600  text-white",
  outline:
    "border border-border bg-transparent hover:bg-neutral-200 dark:hover:bg-primary dark:hover:border-primary ",
};

const setButtonSize: SetButtonSizeProps = {
  large: "px-5 py-3 text-lg",
  small: "px-3 py-1.5 text-sm",
  medium: "px-4 py-2 text-base",
};
const Button: React.FC<ButtonProps> = ({
  style,
  onPress,
  className,
  size = "medium",
  loading = false,
  disabled = false,
  children = "Button",
  htmlType = "button",
  variant = "primary",
  ...props
}) => {
  //focus:ring-2 focus:ring-primary-300 focus:ring-offset-2
  return (
    <button
      type={htmlType}
      onClick={onPress}
      style={{ cursor: loading ? "progress" : "pointer", ...style }}
      disabled={disabled || loading}
      className={`${loading ? "progress" : "auto"} ${setButtonSize[size]} ${
        setVariantCssHandler[variant]
      } font-medium transition focus:outline-none   rounded-md py-3 ${className} `}
      {...props}
    >
      {children}
    </button>
  );
};
export default Button;
