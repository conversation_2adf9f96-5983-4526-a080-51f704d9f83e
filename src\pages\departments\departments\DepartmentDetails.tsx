import { useNavigate, useParams } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Hash, FileText } from "lucide-react";
// import { RoomStatus } from "@/interfaces/master/rooms";
import Button from "@/components/button";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { clearRoomByIdSuccess } from "@/actions/slices/room";
import View from "@/components/view";
import Text from "@/components/text";
import { useDepartment } from "@/actions/calls/department";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import { GenericStatus } from "@/interfaces";
import BouncingLoader from "@/components/BouncingLoader";

const DepartmentDetails = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const { departmentDetailHandler, cleanUp } = useDepartment();
  // const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const departmentData = useSelector((state: RootState) => state?.department?.departmentDetailData);

  useEffect(() => {
    if (id) {
      departmentDetailHandler(id, () => {}, [], (status) => {
        setIsLoading(status === "pending" ? true : status === "failed" ? true : status === "success" && false);
      }
      );
      // setLoading(false);
    }
    return () => {
      cleanUp();
      dispatch(clearRoomByIdSuccess());
    };
  }, []);

  // if (loading || !departmentData) {
  //   return (
  //     <View className="text-center text-muted py-10">Loading Rooms data...</View>
  //   );
  // }

  return (
    <View className="p-4">
      <BouncingLoader isLoading={isLoading} />
      <View className="space-y-6">
        {/* Header */}
        <View className="flex items-center justify-between mb-6">
          <View>
            <Text as="h1" weight="font-semibold" className="text-2xl ">Department Details</Text>
            {/* <Text  as ="p" className="text-muted-foreground">Department ID: {departmentData?.id || "N/A"}</Text> */}
          </View>
          <Button variant="outline" onClick={() => navigate(-1)}
            className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
        </View>

        {/* Department Information Card */}
        <Card>
          <CardHeader>
            <View className="flex items-center justify-between">
              <View>
                <CardTitle className="text-xl">{departmentData?.name || "N/A"}</CardTitle>
                <View className="flex items-center gap-2 mt-2">
                  <Hash className="h-4 w-4" />
                  D-Code: {departmentData?.code || "N/A"}
                </View>
              </View>
              {/* <Badge variant={departmentData?.is_active ? "success" : "destructive"} className="flex items-center gap-1 text-white">
                {departmentData?.is_active ? "Active" : "Inactive"}
              </Badge> */}
              <Text
              as="span"
              className="inline-flex px-2 py-1 text-xs font-medium rounded-full"
              style={getStatusColorScheme(departmentData?.is_active ? GenericStatus.ACTIVE : GenericStatus.INACTIVE)}
            >
              {departmentData?.is_active ? "Active" : "Inactive"}
            </Text>
            </View>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Description */}
            <View>
              <View className="flex items-center gap-2 mb-2">
                <FileText className="h-4 w-4 text-gray-500" />
                <Text as="h3" className="font-semibold">Description</Text>
              </View>
              <Text as="p" className="text-muted-foreground leading-relaxed">{departmentData?.description || "N/A"}</Text>
            </View>

            {/* Timestamps */}
            {/* <View className="grid md:grid-cols-2 gap-4 pt-4 border-t">
              <View>
                <View className="flex items-center gap-2 mb-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <h3 className="font-semibold text-gray-900">Created At</h3>
                </View>
                <p className="text-gray-700">{formatDate(departmentData.created_at)}</p>
              </View>
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <h3 className="font-semibold text-gray-900">Last Updated</h3>
                </div>
                <p className="text-gray-700">{formatDate(department.updated_at)}</p>
              </div>
            </View> */}
          </CardContent>
        </Card>

        {/* Department Stats Card */}
        {/* <Card>
          <CardHeader>
            <CardTitle>Department Statistics</CardTitle>
            <CardDescription>Overview of department metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">15</div>
                <div className="text-sm text-gray-600">Active Doctors</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">234</div>
                <div className="text-sm text-gray-600">Patients This Month</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">89</div>
                <div className="text-sm text-gray-600">Appointments Today</div>
              </div>
            </div>
          </CardContent>
        </Card> */}
      </View>
    </View>
  );
};

export default DepartmentDetails;
