import Input from "@/components/input";
import { useSelector } from "react-redux";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardContent,
  CardDescription,
} from "@/components/ui/card";
import { RootState } from "@/actions/store";
import useForm from "@/utils/custom-hooks/use-form";
import View from "@/components/view";
import Text from "@/components/text";
import SingleSelector from "@/components/SingleSelector";

interface PrefixesProps {
  errorsOpdPrefix: string;
  errorsIpdPrefix: string;
  errorsTestPrefix: string;
  errorsInvoicePrefix: string;
  errorsPatientPrefix: string;
  errorsPaymentPrefix: string;
  errorsFindingsPrefix: string;
  errorsHospitalPrefix: string;
  errorsAppointmentPrefix: string;
  errorsInvoiceStartNumber: string;
  errorsInvoiceStatus: string;
}

const PrefixesSection: React.FC<PrefixesProps> = ({
  errorsIpdPrefix,
  errorsOpdPrefix,
  errorsTestPrefix,
  errorsPatientPrefix,
  errorsPaymentPrefix,
  errorsInvoicePrefix,
  errorsHospitalPrefix,
  errorsFindingsPrefix,
  errorsAppointmentPrefix,
  errorsInvoiceStartNumber,
  errorsInvoiceStatus,
}) => {
  const settingsData = useSelector(
    (state: RootState) => state.systemSettings.settings
  );
  const { values, handleChange } = useForm(settingsData);
  return (
    <Card className="bg-card">
      <CardHeader>
        <CardTitle>System Prefixes</CardTitle>
        <CardDescription>
          Configure prefixes for various system identifiers
        </CardDescription>
      </CardHeader>
      <CardContent>
        <View className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <View className="space-y-2">
            <Text as="label" className="text-sm font-medium">
              Hospital ID Prefix
            </Text>
            <Input
              id="hospital_prefix"
              name="hospital_prefix"
              placeholder="HOS"
              error={errorsHospitalPrefix}
              value={values?.hospital_prefix}
              onChange={handleChange}
            />

            <p className="text-xs text-text-light">
              Used for hospital identifiers
            </p>
          </View>

          <View className="space-y-2">
            <Text as="label" className="text-sm font-medium">
              Patient ID Prefix
            </Text>
            <Input
              id="patient_prefix"
              name="patient_prefix"
              placeholder="PAT"
              error={errorsPatientPrefix}
              value={values?.patient_prefix}
              onChange={handleChange}
            />
            <Text as="p" className="text-xs text-text-light">
              Used for patient identifiers
            </Text>
          </View>

          <View className="space-y-2">
            <Text as="label" className="text-sm font-medium">
              IPD Prefix
            </Text>
            <Input
              id="ipd_prefix"
              name="ipd_prefix"
              placeholder="IPD"
              error={errorsIpdPrefix}
              value={values?.ipd_prefix}
              onChange={handleChange}
            />
            <Text as="p" className="text-xs text-text-light">
              For inpatient department
            </Text>
          </View>

          <View className="space-y-2">
            <Text as="label" className="text-sm font-medium">
              OPD Prefix
            </Text>
            <Input
              id="opd_prefix"
              name="opd_prefix"
              placeholder="OPD-"
              error={errorsOpdPrefix}
              value={values?.opd_prefix}
              onChange={handleChange}
            />
            <Text as="p" className="text-xs text-text-light">
              For outpatient department
            </Text>
          </View>

          <View className="space-y-2">
            <Text as="label" className="text-sm font-medium">
              Appointment Prefix
            </Text>
            <Input
              id="appointment_prefix"
              name="appointment_prefix"
              placeholder="APT"
              error={errorsAppointmentPrefix}
              value={values?.appointment_prefix}
              onChange={handleChange}
            />
            <Text as="p" className="text-xs text-text-light">
              For appointments
            </Text>
          </View>

          <View className="space-y-2">
            <Text as="label" className="text-sm font-medium">
              Payment Prefix
            </Text>
            <Input
              id="payment_prefix"
              name="payment_prefix"
              placeholder="PAY"
              error={errorsPaymentPrefix}
              value={values?.payment_prefix}
              onChange={handleChange}
            />
            <Text as="p" className="text-xs text-text-light">
              For payment receipts
            </Text>
          </View>

          <View className="space-y-2">
            <Text as="label" className="text-sm font-medium">
              Findings Prefix
            </Text>
            <Input
              id="findings_prefix"
              name="findings_prefix"
              placeholder="FIN"
              error={errorsFindingsPrefix}
              value={values?.findings_prefix}
              onChange={handleChange}
            />
            <Text as="p" className="text-xs text-text-light">
              For findings
            </Text>
          </View>
          <View className="space-y-2">
          <Text as="label" className="text-sm font-medium">
            Test Prefix
          </Text>
          <Input
            id="test_prefix"
            name="test_prefix"
            placeholder="TES"
            error={errorsTestPrefix}
            value={values?.test_prefix}
            onChange={handleChange}
          />
          <Text as="p" className="text-xs text-text-light mt-2">
            For medical tests
          </Text>
        </View>
          <View className="space-y-2">
            <Text as="label" className="text-sm font-medium">
              Invoice Prefix
            </Text>
            <Input
              id="invoice_prefix"
              name="invoice_prefix"
              placeholder="INV"
              error={errorsInvoicePrefix}
              value={values?.invoice_prefix}
              onChange={handleChange}
            />
            <Text as="p" className="text-xs text-text-light">
              For invoices
            </Text>
          </View>

          
        <View className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
          <View className="space-y-2">
            <Text as="label" className="text-sm font-medium">
              Invoice Start Number
            </Text>
            <Input
            type="number"
            id="invoice_start_number"
            name="invoice_start_number"
            placeholder="Enter Invoice Start Number"
            error={errorsInvoiceStartNumber}
            value={values?.invoice_start_number}
            onChange={handleChange}
            />
          
          <Text as="p" className="text-xs text-text-light">
            For Invoice
          </Text>
          </View>
          <View className="space-y-2 mb-6">
            <Text as="label" className="text-sm font-medium">
              Invoice Status
          </Text>
          <SingleSelector
            id="invoice_status"
            name="invoice_status"
            placeholder="Select Invoice Status"
            options={[
              { label: "True", value: true },
              { label: "False", value: false },
            ]}
            error={errorsInvoiceStatus}
            value={values?.invoice_status ? true : false}
            onChange={handleChange}
          />
          </View>
        </View>

        </View>
        

      </CardContent>
    </Card>
  );
};

export default PrefixesSection;
