import View from "@/components/view";
import Text from "@/components/text";
import Button from "@/components/button";
import React, { useEffect, useState } from "react";
import { ArrowLeft } from "lucide-react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import BouncingLoader from "@/components/BouncingLoader";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import { GenericStatus } from "@/interfaces";
import { useManagement } from "@/actions/calls/management";

const ManagementDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isLoading, ] = useState(false);
  const { managementDetail, cleanUp } = useManagement();
  const managementData = useSelector(
    (state: any) => state.management.managementDetails
  );

  useEffect(() => {
    if (id) {
      managementDetail(id, () => {});
    }
    return () => {
      cleanUp();
    };
  }, [id]);

  return (
    <React.Fragment>
      <BouncingLoader isLoading={isLoading} />
      <View className="space-y-6 container mx-auto py-8">
        {/* Header Section */}
        <View className="flex justify-between items-center mb-6">
          <View>
            <Text
              as="h1"
              weight="font-semibold"
              className="text-2xl md:text-3xl font-bold text-primary dark:text-white"
            >
              Management Details
            </Text>
            <Text as="p" className="text-muted-foreground">
              View detailed information about the Management
            </Text>
          </View>
          <View className="flex gap-3">
            <Button
              variant="outline"
              size="small"
              onClick={() => navigate(-1)}
              className="flex items-center gap-2"
            >
              <ArrowLeft size={16} />
              Back to Management
            </Button>
          </View>
        </View>

        {/* Complaint Information Card */}
        <Card>
          <CardHeader className="pb-2">
            <View className="flex justify-between items-center">
              <Text
                as="span"
                className="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                style={getStatusColorScheme(
                  managementData?.is_active
                    ? GenericStatus.ACTIVE
                    : GenericStatus.INACTIVE
                )}
              >
                {managementData?.is_active}
              </Text>
            </View>
          </CardHeader>
          <CardContent>
            <View className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <View>
                <Text as="h3" className="text-md font-semibold mb-2">
                  Complaint Name
                </Text>
                <Text as="p" className="text-muted-foreground text-sm">
                  {managementData?.management_name || "N/A"}
                </Text>
              </View>
              <View>
                <Text as="h3" className="text-md font-semibold mb-2">
                  Status
                </Text>
                <Text as="p" className="text-muted-foreground text-sm">
                  {managementData?.is_active || "N/A"}
                </Text>
              </View>
            </View>

            <View className="mt-4 p-4 bg-neutral-100 border border-border rounded-md dark:bg-background">
              <Text as="h3" className="text-md font-semibold mb-2">
                Description
              </Text>
              <Text as="p" className="text-sm">
                {managementData?.description || "N/A"}
              </Text>
            </View>
          </CardContent>
        </Card>
      </View>
    </React.Fragment>
  );
};

export default ManagementDetail;
