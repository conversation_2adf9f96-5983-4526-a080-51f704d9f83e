import View from "./view";
import Text from "./text";
import Button from "./button";
import React, { useEffect } from "react";

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  children?: React.ReactNode;
  footer?: React.ReactNode;
  showCloseButton?: boolean;
  size?: "sm" | "md" | "lg" | "xl" | "full";
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  footer,
  showCloseButton = true,
  size = "md",
}) => {
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    if (isOpen) {
      document.addEventListener("keydown", handleEsc);
    }
    return () => {
      document.removeEventListener("keydown", handleEsc);
    };
  }, [isOpen, onClose]);

  const sizeClasses = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    full: "max-w-full mx-4",
  };

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <View
      role="dialog"
      aria-modal="true"
      onClick={handleBackdropClick}
      className="fixed inset-0 z-50 flex items-center justify-center bg-transparent px-4 "
    >
      <View
        className={`w-full ${sizeClasses[size]} bg-white dark:bg-background rounded-lg shadow-lg overflow-hidden border border-border`}
      >
        <View className="p-6 border-b border-neutral-200 flex justify-between items-start">
          <View>
            <Text
              as="h2"
              className="text-xl text-black font-bold text-text-DEFAULT dark:text-white"
            >
              {title}
            </Text>
            {description && (
              <p className="text-text-light mt-1">{description}</p>
            )}
          </View>
          {showCloseButton && (
            <Button
              variant="ghost"
              onClick={onClose}
              className="h-8 w-8 rounded-full flex items-center justify-center  text-black  dark:text-white hover:bg-primary-50"
            >
              <Text as="span" className="text hover:text-primary">
                X
              </Text>
            </Button>
          )}
        </View>

        <View className="p-6 overflow-y-auto" style={{ maxHeight: "80vh" }}>
          {children}
        </View>

        {footer && (
          <View className="bg-neutral-50 p-6 border-t border-neutral-200">
            {footer}
          </View>
        )}
      </View>
    </View>
  );
};

export default Modal;
