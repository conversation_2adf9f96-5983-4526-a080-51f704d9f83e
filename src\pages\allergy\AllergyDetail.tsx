import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import {
  <PERSON>,
  CardHeader,
  CardT<PERSON><PERSON>,
  CardContent,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import Button from "@/components/button";
import View from "@/components/view";
import Text from "@/components/text";
import { useDispatch, useSelector } from "react-redux";
import { useAllergies } from "@/actions/calls/allergies";
import { clearAllergies } from "@/actions/slices/allergies";
import BouncingLoader from "@/components/BouncingLoader";
import { DATE_FORMAT } from "@/utils/urls/frontend";
import dayjs from "dayjs";

const AllergyDetail = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const { allergyDetailHandler, cleanUp } = useAllergies();

  const allergyData = useSelector(
    (state: any) => state.allergies.allergiesDetailData
  );

  useEffect(() => {
    if (id) {
      allergyDetailHandler(
        id,
        () => {},
        [],
        (status) => {
          setIsLoading(status === "pending");
        }
      );
    }

    return () => {
      cleanUp();
      dispatch(clearAllergies());
    };
  }, [id]);

  if (isLoading) {
    return (
      <View className="text-center text-muted py-10">
        Loading allergy data...
      </View>
    );
  }

  return (
    <View className="container mx-auto p-4 space-y-6">
      <BouncingLoader isLoading={isLoading} />

      <View className="flex justify-between items-center">
        <View>
          <Text as="h1" className="text-2xl font-bold">
            Allergy Details
          </Text>
          <Text as="p" className="text-muted-foreground">
            Viewing details for Allergy: {allergyData?.allergen_name}
          </Text>
        </View>
        <View className="flex gap-3">
          <Link to="/">
            <Button onPress={() => navigate(-1)} variant="outline">
              Back to List
            </Button>
          </Link>
        </View>
      </View>

      <Card>
        <CardHeader>
          <View className="flex items-center justify-between">
            <View>
              <CardTitle className="text-2xl">
                {allergyData?.allergen_name || "Unnamed Allergen"}
              </CardTitle>
              {/* <CardDescription>Allergy ID: {allergyData?.id}</CardDescription> */}
            </View>
          </View>
        </CardHeader>

        <CardContent>
          <View className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <DetailField
              label="Allergen Type"
              value={allergyData?.allergen_type}
            />
            <DetailField
              label="Other Allergen Type"
              value={allergyData?.other_allergen_type}
            />
            <DetailField
              label="Documented By"
              value={allergyData?.documented_by}
            />
            <DetailField
              label="Department"
              value={allergyData?.department_type}
            />
            <DetailField
              label="Created At"
              value={dayjs(allergyData?.created_at).format(DATE_FORMAT)}
            />
            <DetailField
              label="Updated At"
              value={dayjs(allergyData?.updated_at).format(DATE_FORMAT)}
            />
          </View>

          <Separator className="my-6" />

          <View>
            <Text
              as="h3"
              className="text-sm font-medium text-muted-foreground mb-2"
            >
              Notes
            </Text>
            <View
              className="p-4 bg-muted/30 rounded-md prose"
              dangerouslySetInnerHTML={{
                __html: allergyData?.notes || "<p>No notes available.</p>",
              }}
            />
          </View>
        </CardContent>
      </Card>
    </View>
  );
};

// Reusable label + value field
const DetailField = ({
  label,
  value,
}: {
  label: string;
  value?: string | null;
}) => (
  <View>
    <Text as="h3" className="text-sm font-medium text-muted-foreground mb-1">
      {label}
    </Text>
    <Text as="p" className="font-medium">
      {value || "N/A"}
    </Text>
  </View>
);

export default AllergyDetail;
