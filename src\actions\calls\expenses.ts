import { useDispatch } from "react-redux";
import Launch<PERSON><PERSON> from "../api";
import { ApiCallback } from "@/interfaces/api";
import {
  EXPENSES_ADD_URL,
  EXPENSES_DELETE_URL,
  EXPENSES_DETAILS_URL,
  EXPENSES_DROPDOWN_URL,
  EXPENSES_EDIT_URL,
  EXPENSES_LIST_URL,
} from "@/utils/urls/backend";
import { AuthPayload } from "@/interfaces/slices/auth";
import {
  expenseDetailSlice,
  expenseDropdownSlice,
  expenseListSlice,
} from "../slices/expenses";
import { LoadingStatus } from "@/interfaces";

const api = new LaunchApi();

export const useExpenses = () => {
  const dispatch = useDispatch();

  const addExpenses = async <T>(
    data: T,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.post(
        EXPENSES_ADD_URL,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            return callback(true, { success: true, data: response.data });
          } else {
            return callback(false);
          }
        },
        data
      );
    } catch (error) {
      callback(false);
    }
  };

  const expensesList = async (
    page: number | string = 1,
    callback: ApiCallback,
    search?: string | null,
    sort_by?: string | null,
    sort_order?: string | null,
    // filter?: string | null
    data?: any,
    isLoading?: (status: LoadingStatus) => void
  ): Promise<void> => {
    try {
      await api.get(
        `${EXPENSES_LIST_URL}?page=${page}${search ? "&search=" + search : ""}${
          sort_by ? "&sort_by=" + sort_by : ""
        }${sort_order ? "&sort_order=" + sort_order : ""}`,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(expenseListSlice(response.data));
            return callback(true);
          } else {
            return callback(false);
          }
        },
        data,
        (status) => {
          isLoading?.(status);
        }
      );
    } catch (error) {
      callback(false);
    }
  };

  const updateExpenses = async (
    id: string,
    data: any,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.put(
        `${EXPENSES_EDIT_URL}/${id}`,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            return callback(true, response.data);
          } else {
            return callback(false);
          }
        },
        data
      );
    } catch (error) {
      callback(false);
    }
  };

  const deleteExpenses = async (
    id: string,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.delete(
        EXPENSES_DELETE_URL,
        id,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            return callback(true, response.data);
          } else {
            return callback(false);
          }
        }
      );
    } catch (error) {
      callback(false);
    }
  };

  const expensesDetail = async (
    id: string,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.get(
        `${EXPENSES_DETAILS_URL}/${id}`,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(expenseDetailSlice(response.data));
            return callback(true, response.data);
          } else {
            return callback(false);
          }
        }
      );
    } catch (error) {
      callback(false);
    }
  };

  const expensesDropdown = async (callback: ApiCallback): Promise<void> => {
    try {
      await api.get(
        EXPENSES_DROPDOWN_URL,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(expenseDropdownSlice(response.data));
            return callback(true, response.data);
          } else {
            return callback(false);
          }
        }
      );
    } catch (error) {
      callback(false);
    }
  };
  const cleanUp = () => {
    api.cleanup();
  };

  return {
    addExpenses,
    expensesList,
    updateExpenses,
    deleteExpenses,
    expensesDetail,
    expensesDropdown,
    cleanUp,
  };
};
