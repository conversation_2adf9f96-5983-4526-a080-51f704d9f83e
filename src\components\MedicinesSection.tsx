import View from "./view";
import Text from "./text";
import { Plus, X } from "lucide-react";
// import Select from "@/components/Select";
import Button from "@/components/button";
// import SearchSelect from "./SearchSelect";
import React, { useEffect, useState } from "react";
// import useForm from "@/utils/custom-hooks/use-form";
// import { Consultation } from "@/interfaces/consultation";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import SingleSelector from "./SingleSelector";
import Input from "./input";

interface Medicine {
  id: string;
  dosage: string;
  timing: string;
  medicines: string;
  take_with: string;
  medicine_days: string;
}

interface MedicinesSectionProps {
  medicineData: any;
  medicinesList: any[];
  errorsDosage?: string;
  errorsTiming?: string;
  errorsTakeWith?: string;
  errorsMedicines?: string;
  errorsMedicineDays?: string;
  onSetHandler: (field: string, value: any) => void; 
}

const MedicinesSection: React.FC<MedicinesSectionProps> = ({
  medicineData,
  errorsDosage,
  errorsTiming,
  errorsTakeWith,
  medicinesList,
  errorsMedicines,
  // errorsMedicineDays,
  onSetHandler,
}) => {
  const [medicines, setMedicines] = useState<Medicine[]>([]);
  // const { onSetHandler } = useForm<Consultation | null>(null);

  useEffect(() => {
    const medicineString =
      typeof medicineData === "string"
        ? medicineData
        : medicineData?.medicines || "";
    const medicineLists = medicineString ? medicineString?.split(",") : [];
    const arrengedMedicineLists = medicineLists?.map(
      (medicine: any, index: number) => {
        const [medicineName, _, dosage, timing, take_with, medicine_days] = medicine?.split("#");
        return {
          id: (index + 1)?.toString(),
          medicines: medicineName || "",
          dosage: dosage || "",
          timing: timing || "",
          take_with: take_with || "",
          medicine_days: medicine_days || "",
        };
      }
    );

    if (arrengedMedicineLists.length > 0) {
      setMedicines(arrengedMedicineLists);
    } else {
      setMedicines([{ id: "1", medicines: "", dosage: "", timing: "", take_with: "", medicine_days: "" }]);
    }
  }, [medicineData]);

  const addMedicine = () => {
    const newId = (medicines.length + 1).toString();
    const newMedicines = [
      ...medicines,
      { id: newId, medicines: "", dosage: "", timing: "", take_with: "", medicine_days: "" },
    ];
    setMedicines(newMedicines);
  };

  const removeMedicine = (id: string) => {
  // if (medicines.length > 1) {
    const newMedicines = medicines.filter((med) => med.id !== id);
    setMedicines(newMedicines);
    
    // Add this part to update the form data
    onSetHandler(
      "medicines",
      newMedicines
        .map(
          (medicine) =>
            `${medicine.medicines}#${medicine.dosage}#${medicine.timing}#${medicine.take_with}#${medicine.medicine_days}`
        )
        .join(",")
    );
  // }
};

  const updateMedicine = (id: string, field: keyof Medicine, value: string) => {
    setMedicines((prev) => {
      const updated = prev.map((medicine) => {
        if (medicine.id === id) {
          return { ...medicine, [field]: value };
        }
        return medicine;
      });
      onSetHandler(
        "medicines",
        updated
          .map(
            (medicine) =>
              `${medicine.medicines}#${medicine.dosage}#${medicine.timing}#${medicine.take_with}#${medicine.medicine_days}`
          )
          .join(",")
      );
      return updated;
    });
  };

  return (
    <Card className="" style={{ backgroundColor: "var(--background" }}>
      <CardHeader>
          <CardTitle>Medicines</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {medicines?.map((medicine) => (
          <View key={medicine?.id} className="rounded-lg p-4 space-y-4 bg-card">
            <View className="flex justify-between items-center">
              <Text as="h4" className="font-medium">
                Medicine {medicine?.id}
              </Text>
              {/* {index > 0 && ( */}
                <Button
                  type="button"
                  variant="danger"
                  onClick={() => removeMedicine(medicine.id)}
                  // disabled={medicines.length === 1}
                >
                  <X className="h-4 w-4" />
                </Button>
              {/* )} */}
            </View>

            <View className="flex flex-wrap gap-2">
              <View className="flex-grow">
                {/* <SearchSelect
                  name="medicines"
                  label="Medicines"
                  selected={medicine.medicines}
                  onSelect={(option) =>
                    updateMedicine(
                      medicine.id,
                      "medicines",
                      option?.value || ""
                    )
                  }
                  options={medicinesList}
                  placeholder="Select Medicine"
                  error={errorsMedicines}
                /> */}
                <SingleSelector
                  id="medicines"
                  label="Medicines"
                  name="medicines"
                  error={errorsMedicines}
                  value={medicine?.medicines || ""}
                  placeholder="Select Medicine"
                  onChange={(value) => {
                    updateMedicine(medicine?.id, "medicines", value);
                  }}
                  options={medicinesList}
                  
                />
              </View>

              <View className="flex-grow">
                {/* <Select
                  label="Dosage"
                  name="dosage"
                  value={medicine.dosage}
                  onChange={(e) => {
                    updateMedicine(medicine.id, "dosage", e.target.value);
                  }}
                  options={[
                    { label: "1-0-0", value: "1-0-0" },
                    { label: "0-1-0", value: "0-1-0" },
                    { label: "0-0-1", value: "0-0-1" },
                    { label: "1-0-1", value: "1-0-1" },
                    { label: "1-1-0", value: "1-1-0" },
                    { label: "0-1-1", value: "0-1-1" },
                    { label: "1-1-1", value: "1-1-1" },
                  ]}
                  placeholder="Select Dosage"
                  error={errorsDosage}
                /> */}
                <SingleSelector
                  id="dosage"
                  label="Dosage"
                  name="dosage"
                  error={errorsDosage}
                  value={medicine?.dosage || ""}
                  placeholder="Select Dosage"
                  onChange={(value) => {
                    updateMedicine(medicine?.id, "dosage", value);
                  }}
                  options={[
  { label: "1-0-0", value: "1-0-0" },
  { label: "0-1-0", value: "0-1-0" },
  { label: "0-0-1", value: "0-0-1" },
  { label: "1-0-1", value: "1-0-1" },
  { label: "1-1-0", value: "1-1-0" },
  { label: "1-1-1", value: "1-1-1" },
  { label: "2-0-0", value: "2-0-0" },
  { label: "0-2-0", value: "0-2-0" },
  { label: "0-0-2", value: "0-0-2" },
  { label: "2-0-2", value: "2-0-2" },
  { label: "2-2-0", value: "2-2-0" },
  { label: "2-2-2", value: "2-2-2" },
  { label: "5ml", value: "5ml" },
  { label: "10ml", value: "10ml" },
  { label: "15ml", value: "15ml" },
  { label: "20ml", value: "20ml" },
  { label: "1/4 tblsp", value: "1/4 tblsp" },
  { label: "1/2 tblsp", value: "1/2 tblsp" },
  { label: "1 tblsp", value: "1 tblsp" },
  { label: "2 tblsp", value: "2 tblsp" },
]}
                />
              </View>

              <View className="flex-grow">
                {/* <Select
                  label="Timing"
                  name="timing"
                  value={medicine.timing}
                  onChange={(e) => {
                    updateMedicine(medicine.id, "timing", e.target.value);
                  }}
                  options={[
                    { label: "Before Food", value: "before-food" },
                    { label: "After Food", value: "after-food" },
                    { label: "With Food", value: "with-food" },
                    { label: "Empty Stomach", value: "empty-stomach" },
                    { label: "Before Sleep", value: "before-sleep" },
                  ]}
                  placeholder="Select Timing"
                  error={errorsTiming}
                /> */}
                <SingleSelector
                  id="timing"
                  label="Timing"
                  name="timing"
                  error={errorsTiming}
                  value={medicine?.timing || ""}
                  placeholder="Select Timing"
                  onChange={(value) => {
                    updateMedicine(medicine?.id, "timing", value);
                  }}
                  options={[
                    { label: "Before Food", value: "before-food" },
                    { label: "After Food", value: "after-food" },
                    { label: "With Food", value: "with-food" },
                    { label: "Empty Stomach", value: "empty-stomach" },
                    { label: "Before Sleep", value: "before-sleep" },
                  ]}
                />
              </View>

              <View className="flex-shrink">
              <SingleSelector
                  id="take_with"
                  label="Take With"
                  name="take_with"
                  error={errorsTakeWith}
                  value={medicine?.take_with || ""}
                  placeholder="Select with what to take"
                  onChange={(value) => {
                    updateMedicine(medicine.id, "take_with", value);
                  }}
                  options={[
                    { label: "Normal water", value: "Normal water" },
                    { label: "Warm water", value: "Warm water" },
                    { label: "Hot water", value: "Hot water" },
                    { label: "Normal milk", value: "Normal milk" },
                    { label: "Warm milk", value: "Warm milk" },
                    { label: "Hot milk", value: "Hot milk" },
                    { label: "Juice", value: "Juice" },
                  ]}
                />
              </View>
              <View className="flex-initial w-24">
              <Input
                id="medicine_days"
                name="medicine_days"
                label="Days"
                value={medicine?.medicine_days || ""}
                placeholder="Enter Days"
                onChange={(e) => {
                  updateMedicine(medicine?.id, "medicine_days", e.target.value);
                }}
              />
            </View>
            </View>
            {/* <View>
              <Input
                id="medicine_days"
                name="medicine_days"
                label="Days to Take Medicine"
                value={medicine?.medicine_days || ""}
                placeholder="Enter Days"
                onChange={(e) => {
                  updateMedicine(medicine?.id, "medicine_days", e.target.value);
                }}
              />
            </View> */}
          </View>
        ))}

        <Button
          type="button"
          variant="primary"
          onClick={addMedicine}
          className="mt-4 flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Medicine
        </Button>
      </CardContent>
    </Card>
  );
};

export default MedicinesSection;
