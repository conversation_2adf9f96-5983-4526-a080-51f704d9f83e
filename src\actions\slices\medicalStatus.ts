import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  testStatus: false,
  servicesStatus: false,
  dietPlanStatus: false,
  diagnosisStatus: false,
  comorbiditiesStatus: false,
  onExaminationStatus: false,
  chiefComplaintStatus: false,
  surgicalHistoryStatus: false,
};

const modelStatusSlice = createSlice({
  name: "modelStatus",
  initialState,
  reducers: {
    setChiefComplaintModel: (state, action) => {
      state.chiefComplaintStatus = action.payload;
    },
    setSurgicalHistoryModel: (state, action) => {
      state.surgicalHistoryStatus = action.payload;
    },
    setComorbiditiesModel: (state, action) => {
      state.comorbiditiesStatus = action.payload;
    },
    setOnExaminationModel: (state, action) => {
      state.onExaminationStatus = action.payload;
    },
    setDiagnosisModel: (state, action) => {
      state.diagnosisStatus = action.payload;
    },
    setTestModel: (state, action) => {
      state.testStatus = action.payload;
    },
    setDietPlanModel: (state, action) => {
      state.dietPlanStatus = action.payload;
    },

    setServicesModel: (state, action) => {
      state.servicesStatus = action.payload;
    },
  },
});

export const {
  setTestModel,
  setDietPlanModel,
  setServicesModel,
  setDiagnosisModel,
  setOnExaminationModel,
  setComorbiditiesModel,
  setChiefComplaintModel,
  setSurgicalHistoryModel,
} = modelStatusSlice.actions;
export default modelStatusSlice.reducer;
