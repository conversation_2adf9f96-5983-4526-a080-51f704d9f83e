import { FistulaState } from "@/interfaces/fistula/index";
import { createSlice } from "@reduxjs/toolkit";

const initialState: FistulaState = {
  fistulaDetailData: {},
  fistulaListData: [],
  fistulaDropdownData: [],
};

const fistulaSlice = createSlice({
  name: "fistula",
  initialState,
  reducers: {
    fistulaDetailSlice: (state, action) => {
      state.fistulaDetailData = action?.payload;
    },
    fistulaListSlice: (state, action) => {
      state.fistulaListData = action?.payload;
    },
    fistulaDropdownSlice: (state, action) => {
      state.fistulaDropdownData = action?.payload;
    },
    clearFistulaDetailSlice: (state) => {
      state.fistulaDetailData = null;
    },
  },
});

export const {
  fistulaDetailSlice,
  fistulaListSlice,
  clearFistulaDetailSlice,
  fistulaDropdownSlice,
} = fistulaSlice.actions;

export default fistulaSlice.reducer;
