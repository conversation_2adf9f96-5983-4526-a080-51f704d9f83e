import { SortOption } from "@/components/SortData";
import { SetURLSearchParams } from "react-router-dom";

// function to handle sorts
export const handleSortChange = (
    option: SortOption,
    setActiveSort: React.Dispatch<React.SetStateAction<SortOption | null>>,
    setSearchParams: SetURLSearchParams,
    searchParams: URLSearchParams
) => {
    setActiveSort(option);
    if(option.order){
        setSearchParams(
            {
                ...Object.fromEntries([...searchParams]),
                currentPage: "1",
                sort_by: option.value,
                sort_order: option.order,
            },
            { replace: true }
        );
    }
};