import { ConsultationState } from "@/interfaces/slices/consultation";
import { createSlice } from "@reduxjs/toolkit";

const initialState: ConsultationState = {
  consultationDetailData: {},
  consultationListData: [],
  consultationDropdownData: [],
  loading: false,
  consultationAmount: 0,
};

const consultationSlice = createSlice({
  name: "consultation",
  initialState,
  reducers: {
    consultationListSlice: (state, action) => {
      state.consultationListData = action?.payload;
      state.loading = false;
    },
    consultationDetailSlice: (state, action) => {
      state.consultationDetailData = action?.payload;
      state.loading = false;
    },
    consultationDropdownSlice: (state, action) => {
      state.consultationDropdownData = action?.payload;
      state.loading = false;
    },
    clearConsultationDetailSlice: (state) => {
      state.consultationDetailData = null;
    },
    setConsultationAmount: (state, action) => {
      state.consultationAmount =
        action.payload === "add"
          ? action?.payload.amount + state.consultationAmount
          : action?.payload.amount;
    },
    clearConsultationAmount: (state) => {
      state.consultationAmount = 0;
    },
  },
});

export const {
  consultationDetailSlice,
  consultationListSlice,
  clearConsultationAmount,
  setConsultationAmount,
  clearConsultationDetailSlice,
  consultationDropdownSlice,
} = consultationSlice.actions;
export default consultationSlice.reducer;
