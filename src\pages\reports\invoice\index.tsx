import Filter from "@/pages/filter";
import Text from "@/components/text";
import View from "@/components/view";
import Input from "@/components/input";
import Button from "@/components/button";
import { RootState } from "@/actions/store";
import { Card } from "@/components/ui/card";
import DataSort from "@/components/SortData";
import InfoCard from "@/components/ui/infoCard";
import { Banknote, CircleDollarSign, FileText, Percent, Smartphone, TrendingUp, UserX } from "lucide-react";
import { useSearchParams } from "react-router-dom";
import React, { useEffect, useState } from "react";
import SearchBar from "@/components/ui/search-bar";
import { useDispatch, useSelector } from "react-redux";
import DynamicTable from "@/components/ui/DynamicTable";
import BouncingLoader from "@/components/BouncingLoader";
import PaginationComponent from "@/components/Pagination";
import { handleSortChange } from "@/utils/helperFunctions";
import DateRangePicker from "@/components/DateRangePicker";
import { clearList } from "@/actions/slices/invoiceReport";
import { INVOICE_REPORT_DOWNLOAD_URL } from "@/utils/urls/backend";
import { useInvoiceReport } from "@/actions/calls/reports/invoice";
import SingleSelector from "@/components/SingleSelector";
import { useAmountType } from "@/actions/calls/amountType";
import { Separator } from "@/components/ui/separator";

const Invoice: React.FC<{}> = ({}) => {
  //   const navigate = useNavigate();
  const dispatch = useDispatch();
  const [loadingStatus, setIsLoading] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const { amountTypeDropdownHandler } = useAmountType();
  const invoiceReportList = useSelector(
    (state: RootState) => state.invoiceReport.invoiceReportList
  );

  const currencySymbol = useSelector(
    (state: RootState) => state.systemSettings.settings.currency_symbol
  );
  
  
  
  

  const [filterData, setFilterData] = useState<null | Record<string, string>>(
    null
  );

  const { cleanUp, getListApi } = useInvoiceReport();

  const sortOptions: any[] = [
    { label: "Patient Name (A-Z)", value: "patient_name", order: "asc" },
    { label: "Patient Name (Z-A)", value: "patient_name", order: "desc" },
    { label: "Patient Email (A-Z)", value: "patient_email", order: "asc" },
    { label: "Patient Email (Z-A)", value: "patient_email", order: "desc" },
    { label: "Patient Phone (A-Z)", value: "patient_phone", order: "asc" },
    { label: "Patient Phone (Z-A)", value: "patient_phone", order: "desc" },
    { label: "Patient Number (A-Z)", value: "patient_number", order: "asc" },
    { label: "Patient Number (Z-A)", value: "patient_number", order: "desc" },
    { label: "Doctor Name (A-Z)", value: "doctor_name", order: "asc" },
    { label: "Doctor Name (Z-A)", value: "doctor_name", order: "desc" },
    { label: "Doctor Email (A-Z)", value: "doctor_email", order: "asc" },
    { label: "Doctor Email (Z-A)", value: "doctor_email", order: "desc" },
    { label: "Doctor Phone (A-Z)", value: "doctor_phone", order: "asc" },
    { label: "Doctor Phone (Z-A)", value: "doctor_phone", order: "desc" },
    {
      label: "Referred By Name (A-Z)",
      value: "referred_by_name",
      order: "asc",
    },
    {
      label: "Referred By Name (Z-A)",
      value: "referred_by_name",
      order: "desc",
    },
    {
      label: "Collected Amount (A-Z)",
      value: "collected_amount",
      order: "asc",
    },
    {
      label: "Collected Amount (Z-A)",
      value: "collected_amount",
      order: "desc",
    },
    { label: "Balance Amount (A-Z)", value: "balanced_amount", order: "asc" },
    { label: "Balance Amount (Z-A)", value: "balanced_amount", order: "desc" },
  ];

  const amountTypeData = useSelector(
    (state: RootState) => state.amountType.amountTypeDropdownData
  );

  useEffect(() => {
    amountTypeDropdownHandler(() => {});
  }, []);
  

  const [activeSort, setActiveSort] = useState<any | null>(sortOptions[0]);

  useEffect(() => {
    getListApi(
      searchParams.get("page") ?? 1,
      () => {},
      (loadingStatus) => {
        setIsLoading(
          loadingStatus == "pending"
            ? true
            : loadingStatus == "failed"
            ? true
            : loadingStatus == "success" && false
        );
      },
      searchParams.get("search") ?? null,
      searchParams.get("sort_by") ?? null,
      searchParams.get("sort_order") ?? null,
      searchParams?.get("from_date") ?? null,
      searchParams?.get("to_date") ?? null,
      filterData
    );
    return () => {
      cleanUp();
      dispatch(clearList());
    };
  }, [
    filterData,
    searchParams.get("page"),
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams?.get("to_date"),
    searchParams?.get("from_date"),
    searchParams.get("sort_order"),
  ]);

    // console.log(invoiceReportList, "invoiceReportList");
    const cashAmount = invoiceReportList?.typesOfPayment.length > 0 ? invoiceReportList?.typesOfPayment?.filter((item: any) => (
     item.payment_type === "Cash"
  ))[0].total_collected : 0;

  const upiOrOnlineAmount = invoiceReportList?.typesOfPayment.length > 0 ? invoiceReportList?.typesOfPayment?.filter((item: any) => (
     item.payment_type !== "Cash"
  )).reduce((acc: any, item: any) => acc + item.total_collected, 0) : 0;

  const discountAmount = invoiceReportList?.paymentBreakPoint.length > 0 ? invoiceReportList?.paymentBreakPoint?.reduce((acc: any, item: any) => acc + Number(item.total_discount), 0) : 0;

  const downloadExpensesExcel = async () => {
    try {
      setIsLoading(true);
      const baseUrl = import.meta.env.VITE_BASE_URL;
      const token = localStorage.getItem("token");

      const response = await fetch(
        `${baseUrl}${INVOICE_REPORT_DOWNLOAD_URL}?page=${
          searchParams.get("page") ?? 1
        }${
          searchParams.get("search")
            ? "&search=" + searchParams.get("search")
            : ""
        }${
          searchParams.get("sort_by")
            ? "&sort_by=" + searchParams.get("sort_by")
            : ""
        }${
          searchParams.get("sort_order")
            ? "&sort_order=" + searchParams.get("sort_order")
            : ""
        }${
          searchParams?.get("from_date")
            ? "&from_date=" + searchParams?.get("from_date")
            : ""
        }${
          searchParams?.get("to_date")
            ? "&to_date=" + searchParams?.get("to_date")
            : ""
        }`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            Accept:
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          },
        }
      );

      if (!response.ok) {
        setIsLoading(false);
        throw new Error("Excel download failed");
      }
      setIsLoading(false);

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "expenses-report.xlsx");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      window.URL.revokeObjectURL(url);
    } catch (error) {
      setIsLoading(false);
      alert("Failed to download Excel report");
    }
  };

  return (
    <React.Fragment>
      <View className="fixed top-4 left-0  w-full z-50">
        <BouncingLoader isLoading={loadingStatus} />
      </View>
      <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4  gap-6 mb-8">
        <InfoCard
          label="Amount billed"
          valueStyle="!text-blue-400 !text-3xl"
          icon={<FileText />}
          iconStyle="!bg-blue-500/20 !text-blue-400"
          value={`${currencySymbol}${invoiceReportList?.collected_amount}` || "N/A"}
          className="dark:bg-card rounded-2xl shadow-md transition-all duration-300 ease-in-out hover:scale-[1.02] hover:shadow-lg"
        />
        <InfoCard
     
          label="Amount collected"
          valueStyle="!text-green-400 !text-3xl"
          icon={<TrendingUp />}
          iconStyle="!bg-green-500/20 !text-green-400"
          value={`${currencySymbol}${invoiceReportList?.includeInvoiceAmount}` || "N/A"}
          className="dark:bg-card rounded-2xl shadow-md transition-all duration-300 ease-in-out hover:scale-[1.02] hover:shadow-lg"
        />
        <InfoCard
          // label="Exclude Invoice Amount"
          // label="Amount collected"
          label="Cancelled by patient"
          valueStyle="!text-red-400 !text-3xl"
          icon={<UserX />}
          iconStyle="!bg-red-500/20 !text-red-400"
          value={`${currencySymbol}${invoiceReportList?.excludeInvoiceAmount}` || "N/A"}
          className="dark:bg-card rounded-2xl shadow-md transition-all duration-300 ease-in-out hover:scale-[1.02] hover:shadow-lg"
        />
        <InfoCard
          // label="Exclude Invoice Amount"
          // label="Amount collected"
          label="Discount amount"
          valueStyle="!text-orange-400 !text-3xl"
          icon={<Percent />}
          iconStyle="!bg-orange-500/20 !text-orange-400"
          value={`${currencySymbol}${discountAmount}` || "N/A"}
          className="dark:bg-card rounded-2xl shadow-md transition-all duration-300 ease-in-out hover:scale-[1.02] hover:shadow-lg"
        />
      </View>
      <Separator />
      {/* <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8 mt-8">
        {invoiceReportList?.typesOfPayment?.map((item: any, index: number) => (
          <InfoCard
            key={index}
            label={item?.payment_type}
            valueStyle="!text-primary"
            // icon={< />}
            value={
              <Text>
                <strong>Total Collected</strong>: Rs {item?.total_collected}
              </Text>
            }
            // subValue={`Total Balance : ${item?.total_balanced}`}
            className="dark:bg-card rounded-2xl shadow-md transition-all duration-300 ease-in-out hover:scale-[1.02] hover:shadow-lg"
          />
        ))}
      </View> */}
      <View className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 mt-8">
 <InfoCard
          label="Cash collection"
          valueStyle="!text-emerald-400 !text-3xl"
          icon={<Banknote />}
          iconStyle="!bg-emerald-500/20 !text-emerald-400"
          value={`${currencySymbol}${cashAmount}` || "N/A"}
          className="dark:bg-card rounded-2xl shadow-md transition-all duration-300 ease-in-out hover:scale-[1.02] hover:shadow-lg"
        />
        <InfoCard
     
          label="UPI/online collection"
          valueStyle="!text-purple-400 !text-3xl"
          icon={<Smartphone />}
          iconStyle="!bg-purple-500/20 !text-purple-400"
          value={`${currencySymbol}${upiOrOnlineAmount}` || "N/A"}
          className="dark:bg-card rounded-2xl shadow-md transition-all duration-300 ease-in-out hover:scale-[1.02] hover:shadow-lg"
        />
        {/* {invoiceReportList?.paymentBreakPoint?.map(
          (item: any, index: number) => (
            <InfoCard
              key={index}
              label={item?.amount_for}
              valueStyle="!text-primary"
              // icon={< />}
              value={
                <>
                  <Text>
                    <strong>Total amount</strong>: Rs {item?.total_amount}
                  </Text>
               
                </>
              }
              className="dark:bg-card rounded-2xl shadow-md transition-all duration-300 ease-in-out hover:scale-[1.02] hover:shadow-lg"
            />
          )
        )} */}
      </View>
      <View className="mb-6 flex justify-between items-center">
        <View className="mb-6">
          <Text
            as="h1"
            weight="font-semibold"
            className="text-2xl font-bold text-text-DEFAULT mb-1"
          >
            Invoice Report
          </Text>
          <Text as="p" className="text-text-light">
            Manage Invoice Report
          </Text>
        </View>
        <View className="flex gap-4">
          {invoiceReportList?.table?.data?.length > 0 && (
            <Button variant="primary" onPress={downloadExpensesExcel}>
              Download Invoice Reports
            </Button>
          )}
          <View>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
              Select Date Range
            </label>
            <DateRangePicker
              // onDateChange={handleDateChange}
              placeholder="Choose your dates"
            />
          </View>
        </View>
      </View>

      <Card className="overflow-hidden">
        {/* Table */}
        <DynamicTable
          // isLoading={loadingStatus}
          tableHeaders={[
            "Patient Name",
            "Patient Email",
            "Patient Phone",
            "Patient Number",
            "Doctor Name",
            "Doctor Email",
            "Doctor Phone",
            "Referred By Name",
            "Collected Amount",
            // "Balance Amount",
            "Mode of Payment",
          ]}
          tableData={invoiceReportList?.table?.data?.map((invoice: any) => [
            invoice.patient_name,
            invoice.patient_email,
            invoice.patient_phone,
            invoice.patient_number,
            invoice.doctor_name,
            invoice.doctor_email,
            invoice.doctor_phone,
            invoice.referred_by_name,
            `Rs ${invoice.collected_amount}`,
            // `Rs ${invoice.balanced_amount}`,
            invoice.payment_type,
          ])}
          header={{
            search: (
              <SearchBar
                onSearch={(val) =>
                  setSearchParams({
                    ...Object.fromEntries(searchParams),
                    search: val,
                    currentPage: "1",
                  })
                }
              />
            ),
            filter: (
              <Filter
                onResetFilter={() => {
                  setFilterData(null);
                }}
                title="Expense Filter"
                onFilterApiCall={(data) => {
                  setFilterData({
                    multiple_filter: data,
                  });
                }}
                inputFields={[
                  <View className="w-full my-4">
                    <Input name="patient_name" placeholder="Patient name" />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="doctor_name" placeholder="Doctor name" />
                  </View>,
                  <View className="w-full my-4">
                    <Input
                      name="referred_by_name"
                      placeholder="Referred by name"
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Input
                      name="collected_amount"
                      placeholder="Collected amount"
                      type="number"
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Input
                      name="balanced_amount"
                      placeholder="Balanced amount"
                      type="number"
                    />
                  </View>,
                  <View className="w-full my-4">
                    <SingleSelector
                      required={true}
                      id="payment_type"
                      name="payment_type"
                      label="Mode of Payment"
                      options={amountTypeData?.map((item: any) => ({
                        value: item.amount_for,
                        label: item.amount_for,
                      }))}
                      placeholder="Select Mode of Payment"
                    />
                  </View>,
                ]}
              />
            ),
            sort: (
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
            ),
          }}
          footer={{
            pagination: (
              <PaginationComponent
                current_page={invoiceReportList?.table?.current_page}
                last_page={invoiceReportList?.table?.last_page}
                getPageNumberHandler={(page) =>
                  setSearchParams(
                    {
                      ...Object.fromEntries(searchParams),
                      currentPage: `${page}`,
                    },
                    { replace: true }
                  )
                }
              />
            ),
          }}
        />
      </Card>
    </React.Fragment>
  );
};

export default Invoice;
