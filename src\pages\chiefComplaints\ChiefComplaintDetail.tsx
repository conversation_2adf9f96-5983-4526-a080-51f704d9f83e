import View from "@/components/view";
import Text from "@/components/text";
import Button from "@/components/button";
import React, { useEffect, useState } from "react";
import { ArrowLeft } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { useChiefComplaint } from "@/actions/calls/chiefComplaints";
import BouncingLoader from "@/components/BouncingLoader";
import { clearChiefComplaintDetailSlice } from "@/actions/slices/chiefComplaints";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import { GenericStatus } from "@/interfaces";

const ChiefComplaintDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);

  // const { findingDetailHandler, cleanUp } = useFindings();
  const { chiefComplaintDetailHandler, cleanUp } = useChiefComplaint();
  const complaintData = useSelector(
    (state: any) => state.chiefComplaint.chiefComplaintDetailData
  );

  useEffect(() => {
    if (id) {
      chiefComplaintDetailHandler(
        id,
        (_: boolean) => {},
        [],
        (status) => {
          setIsLoading(
            status === "pending"
              ? true
              : status === "failed"
              ? true
              : status === "success" && false
          );
        }
      );
    }
  }, [id]);

  useEffect(() => {
    return () => {
      cleanUp();
      dispatch(clearChiefComplaintDetailSlice());
    };
  }, [dispatch]);

  return (
    <React.Fragment>
      <BouncingLoader isLoading={isLoading} />
      <View className="space-y-6 container mx-auto py-8">
        {/* Header Section */}
        <View className="flex justify-between items-center mb-6">
          <View>
            <Text
              as="h1"
              weight="font-semibold"
              className="text-2xl md:text-3xl font-bold text-primary dark:text-white"
            >
              Chief Complaint Details
            </Text>
            <Text as="p" className="text-muted-foreground">
              View detailed information about the chief complaint
            </Text>
          </View>
          <View className="flex gap-3">
            <Button
              variant="outline"
              size="small"
              onClick={() => navigate(-1)}
              className="flex items-center gap-2"
            >
              {/* <Link to="/chief-complaints" className="flex items-center gap-2"> */}
              <ArrowLeft size={16} />
              Back to Complaints
              {/* </Link> */}
            </Button>
          </View>
        </View>

        {/* Complaint Information Card */}
        <Card>
          <CardHeader className="pb-2">
            <View className="flex justify-between items-center">
              <CardTitle className="text-lg">Complaint Information</CardTitle>
              <Text
                as="span"
                className="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                // className={`px-3 py-1 rounded-full text-xs font-medium ${
                //   complaintData?.is_active === "Active"
                //     ? "bg-green-100 text-green-800"
                //     : complaintData?.is_active === "Inactive"
                //     ? "bg-gray-100 text-gray-800"
                //     : "bg-gray-100 text-gray-800"
                // }`}
                style={getStatusColorScheme(
                  complaintData?.is_active
                    ? GenericStatus.ACTIVE
                    : GenericStatus.INACTIVE
                )}
              >
                {complaintData?.is_active}
              </Text>
            </View>
          </CardHeader>
          <CardContent>
            <View className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <View>
                <Text as="h3" className="text-md font-semibold mb-2">
                  Complaint Name
                </Text>
                <Text as="p" className="text-muted-foreground text-sm">
                  {complaintData?.complaint_name || "N/A"}
                </Text>
              </View>
              <View>
                <Text as="h3" className="text-md font-semibold mb-2">
                  Status
                </Text>
                <Text as="p" className="text-muted-foreground text-sm">
                  {complaintData?.is_active || "N/A"}
                </Text>
              </View>
            </View>

            <View className="mt-4 p-4 bg-neutral-100 border border-border rounded-md dark:bg-background">
              <Text as="h3" className="text-md font-semibold mb-2">
                Description
              </Text>
              <Text as="p" className="text-sm">
                {complaintData?.description || "N/A"}
              </Text>
            </View>
          </CardContent>
        </Card>

        {/* System Information Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">System Information</CardTitle>
          </CardHeader>
          <CardContent>
            <View className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <View>
                <Text as="h3" className="text-md font-semibold mb-2">
                  Created Date
                </Text>
                <Text as="p" className="text-muted-foreground">
                  {complaintData?.created_at
                    ? new Date(complaintData?.created_at)?.toLocaleDateString()
                    : "N/A"}
                </Text>
              </View>
              <View>
                <Text as="h3" className="text-md font-semibold mb-2">
                  Last Updated
                </Text>
                <Text as="p" className="text-muted-foreground">
                  {complaintData?.updated_at
                    ? new Date(complaintData?.updated_at)?.toLocaleDateString()
                    : "N/A"}
                </Text>
              </View>
            </View>
          </CardContent>
        </Card>
      </View>
    </React.Fragment>
  );
};

export default ChiefComplaintDetail;
