import { textareaProps } from "@/interfaces/components/input/input";
import View from "./view";

const Textarea: React.FC<textareaProps> = ({
  type = "text",
  onChange,
  onBlur,
  disabled = false,
  placeholder = "Enter text",
  value,
  // defaultValue,
  variant = "default",
  fullWidth = false,
  id,
  name,
  ref,
  style,
  className,
  leftIcon,
  rightIcon,
  error,
  label,
  required = false,
  ...rest
}) => {
  const setVariantHandler = (variant: any) => {
    if (variant == "danger") {
      return "border-danger focus:ring-danger/30";
    }
    if (variant == "filled") {
      return "w-full p-3 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-300 transition bg-primary";
    } else {
      return "w-full p-3 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-300 transition";
    }
  };

  return (
    <View>
      {label && (
        <label htmlFor={name} className="block mb-0.5 text-text-dark">
          {label}
          {required && <span className="text-red-600">*</span>}
        </label>
      )}
      <View className={`input-wrapper ${fullWidth ? "w-full" : ""}`}>
        {leftIcon && <View className="input-icon-left">{leftIcon}</View>}
        <textarea
          ref={ref}
          onChange={onChange}
          onBlur={onBlur}
          disabled={disabled}
          placeholder={placeholder}
          value={value + ""}
          // defaultValue={defaultValue}
          id={id}
          name={name}
          className={`w-full p-3 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-300 transition dark:bg-background ${setVariantHandler(
            variant
          )} input-${variant} ${className || ""}`}
          style={style}
          {...rest}
        ></textarea>
      </View>
      {error && <p className="text-red-500 text-sm">{error}</p>}
    </View>
  );
};
export default Textarea;
