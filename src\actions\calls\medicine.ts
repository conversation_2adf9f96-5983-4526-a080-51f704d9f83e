import { useDispatch } from "react-redux";
import Launch<PERSON><PERSON> from "../api";
import { ApiCallback } from "@/interfaces/api";
import {
  MEDICINE_DROPDOWN_URL,
  MEDICINES_ADD_URL,
  MEDICINES_DELETE_URL,
  MEDICINES_DETAIL_URL,
  MEDICINES_EDIT_URL,
  MEDICINES_LIST_URL,
} from "@/utils/urls/backend";
import { AuthPayload } from "@/interfaces/slices/auth";
import {  medicineDetailReducer, medicineDropdownReducer, medicineListReducer } from "../slices/medicine";
import { GENERIC_ERROR_MESSAGE } from "@/utils/message";
import { LoadingStatus } from "@/interfaces";


const api = new LaunchApi();

export const useMedicine = () => {
  const dispatch = useDispatch();

  const addMedicineHandler = async <T>(
    data: T,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.post(
        MEDICINES_ADD_URL,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            return callback(true, response.data);
          } else {
            return callback(false);
          }
        },
        data
      );
    } catch (error) {
      callback(false);
    }
  };

  const medicineListHandler = async (
      page: number | string = 1,
      callback: ApiCallback,
      search?: string | null,
      sort_by?: string | null,
      sort_order?: string | null,
      data?: any,
      isLoading?: (status: LoadingStatus) => void
    ): Promise<void> => {
      try {
        
        await api.get(
          `${MEDICINES_LIST_URL}?page=${page}${search ? "&search=" + search : ""}${
            sort_by ? "&sort_by=" + sort_by : ""
          }${sort_order ? "&sort_order=" + sort_order : ""}`,
          (response: AuthPayload, success: boolean, statusCode: number) => {
            
            if (success && statusCode === 200) {
              dispatch(medicineListReducer(response?.data));
              return callback(true, response.data);
            } else {
              callback(true, { success: false });
            }
          },
          data,
          (status) => {
            isLoading?.(status);
          }
        );
      } catch (error) {
        callback(false, { success: false, error: GENERIC_ERROR_MESSAGE });
      }
    };
  
    const medicineDetailHandler = async (
      id: string,
      callback: ApiCallback,
      data?: any,
      isLoading?: (status: LoadingStatus) => void
    ): Promise<void> => {
      try {
        await api.get(
          MEDICINES_DETAIL_URL + "/" + id,
          (response: AuthPayload, success: boolean, statusCode: number) => {
            if (success && statusCode === 200) {
              dispatch(medicineDetailReducer(response));
              return callback(true, response.data);
            } else {
              callback(false, { success: false });
            }
          },
          data,
          (status) => {
            isLoading?.(status);
          }
        );
      } catch (error) {
        callback(false, { success: false, error: GENERIC_ERROR_MESSAGE });
      }
    };

    const editMedicineHandler = async (
      id: string,
      data: any,
      callback: ApiCallback
    ): Promise<void> => {
      try {
        await api.put(
          MEDICINES_EDIT_URL + "/" + id,
          (response: AuthPayload, success: boolean, statusCode: number) => {
            if (success && statusCode === 200) {
              return callback(true, {
                success: true,
                message: response.data?.message,
              });
            } else if (success && statusCode !== 204) {
              return callback(false, { success: false });
            }
          },
          data
        );
      } catch (error) {
        callback(false, { success: false });
      }
    };

  const deleteMedicineHandler = async (
    id: string,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.delete(
        MEDICINES_DELETE_URL,
        id,
        (_: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            return callback(true);
          } else {
            return callback(false);
          }
        }
      );
    } catch (error) {
      callback(false);
    }
  };

  const medicineDropdownHandler = async (
    callback: ApiCallback,
    fieldName:string|string[] = 'medicine_name',
    departmentValue?: string
  ): Promise<void> => {
    try {
      await api.post(
        MEDICINE_DROPDOWN_URL,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(medicineDropdownReducer(response.data));
            return callback(true);
          } else {
            return callback(false);
          }
        },{
          field_name: fieldName,
          departmentValue: departmentValue ?? "All",
        }
      );
    } catch (error) {
      callback(false);
    }
  };


  const cleanUp = () => {
    api.cleanup();
  };

  return {
    cleanUp,
    addMedicineHandler,
    medicineListHandler,
    medicineDetailHandler,
    editMedicineHandler,
    deleteMedicineHandler,
    medicineDropdownHandler,
  };
};
