import View from "@/components/view";
import Text from "@/components/text";
import SectionOne from "./SectionOne";
import LaunchApi from "@/actions/api";
import Input from "@/components/input";
import Button from "@/components/button";
import ServiceModel from "./serviceModel";
import { RootState } from "@/actions/store";
import { GenericStatus } from "@/interfaces";
import Textarea from "@/components/Textarea";
import PaymentSection from "./PaymentSection";
import React, { useEffect, useState } from "react";
import { useInvoice } from "@/actions/calls/invoice";
import { toast } from "@/utils/custom-hooks/use-toast";
import { useDispatch, useSelector } from "react-redux";
import SingleSelector from "@/components/SingleSelector";
import BouncingLoader from "@/components/BouncingLoader";
import { useNavigate, useParams } from "react-router-dom";
import { useAmountType } from "@/actions/calls/amountType";
import { INVOICE_ADD_OR_UPDATE_URL } from "@/utils/urls/backend";
import { clearYogaAsanaDetailSlice } from "@/actions/slices/yogaAsana";
import { statusOptions } from "../forms/consultationForm/consultationFormOptions";

const api = new LaunchApi();

const InvoiceDetail: React.FC = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [amount, setAmount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [collectedAmount, setCollectedAmount] = useState<number>(0);
  const [additionalAmountReason, setAdditionalAmountReason] = useState<
    string | null
  >(null);
  const [taxAmount] = useState<number>(0);
  const [type, setType] = useState<boolean>(false);
  const { getPaymentDetailHandler, getInvoiceDetailHandler, cleanUp } =
    useInvoice();
  const [paymentType, setPaymentType] = useState<string>("");
  const [transactionId, setTransactionId] = useState<string>("");
  const [paymentStatus, setPaymentStatus] = useState<string>("");
  const [status, setStatus] = useState<string>("");
  const { amountTypeDropdownHandler } = useAmountType();

  const invoiceData = useSelector(
    (state: RootState) => state.invoice.invoiceDetailData
  );
  const amountTypeData = useSelector(
    (state: RootState) => state.amountType.amountTypeDropdownData
  );

  useEffect(() => {
    if (id) {
      amountTypeDropdownHandler(() => {});
      getPaymentDetailHandler(id, () => {});
      getInvoiceDetailHandler(
        id,
        () => {},
        [],
        (status) => {
          setIsLoading(
            status === "pending"
              ? true
              : status === "failed"
              ? true
              : status === "success" && false
          );
        }
      );
    }
    return () => {
      cleanUp();
      dispatch(clearYogaAsanaDetailSlice());
    };
  }, [id]);

  useEffect(() => {
    setPaymentStatus(invoiceData?.payment_status);
    setCollectedAmount(invoiceData?.collected_amount);
    setPaymentType(invoiceData?.payment_type);
    setType(invoiceData?.type !== "Follow-up" ? true : false);
    setStatus(invoiceData?.status);
    setAdditionalAmountReason(invoiceData?.additional_amount_reason);
    // setAmount(
    //   invoiceData?.discount_total_amount
    //     ? Number(invoiceData?.discount_total_amount)
    //     : Number(invoiceData.total_amount)
    // );
    setAmount(Number(invoiceData?.prefill_amount));
  }, [
    invoiceData?.type,
    invoiceData?.status,
    invoiceData?.payment_type,
    invoiceData?.payment_status,
    invoiceData?.prefill_amount,
    invoiceData?.balanced_amount,
    invoiceData?.collected_amount,
    invoiceData?.additional_amount_reason,
  ]);

  const submitData = () => {
    api.post(
      `${INVOICE_ADD_OR_UPDATE_URL}/${invoiceData?.id}`,
      (_, success) => {
        if (success) {
          navigate(-1);
          toast({
            title: "Success!",
            description: "Successfully Amount added",
            variant: "success",
          });
          if (id) {
            getInvoiceDetailHandler(id, () => {});
            getPaymentDetailHandler(id, () => {});
          }
        }
      },
      {
        status: status,
        tax_amount: taxAmount,
        payment_type: paymentType,
        paymentStatus: paymentStatus,
        transaction_id: transactionId,
        columnName: "consultation_id",
        consultationId: invoiceData?.id,
        discount_amount: invoiceData?.discount_amount,
        collected_amount:
          Number(invoiceData?.collected_amount || 0) + Number(amount),
        // balanced_amount:
        //   Number(invoiceData?.total_amount || 0) -
        //   (Number(invoiceData?.collected_amount || 0) + Number(amount)),
        balanced_amount: 0,
      }
    );
  };

  const handleSubmit = () => {
    // // Validation check
    // if (!paymentType && !transactionId && !amount) {
    //   return alert("Please select payment type and transaction id");
    // }

    // // Check if amount is less than total amount
    // if (amount < invoiceData?.total_amount - invoiceData?.collected_amount) {
    //   const confirmed = confirm(
    //     "Amount is less than total. Do you want to continue?"
    //   );
    //   if (!confirmed) {
    //     return; // Exit early if user cancels
    //   }
    // }

    // // Check if amount is more than total amount
    // if (amount > invoiceData?.total_amount) {
    //   return alert("Amount is more than total amount");
    // }

    // Only call submitData if all validations pass
    submitData();
  };

  return (
    <View className="min-h-screen p-8">
      <BouncingLoader isLoading={isLoading} />
      <ServiceModel>
        <View className="max-w-4xl mx-auto">
          {/* <SectionOne type={type} testTotalAmountData={testTotalAmountData} /> */}
          <SectionOne
            type={type}
            balanceAmount={
              <>
                {!!invoiceData?.balanced_amount && (
                  <Text className="text-muted-foreground text-sm">
                    Balance Amount : ₹{invoiceData?.balanced_amount}
                  </Text>
                )}
              </>
            }
          />
          <hr style={{ margin: "20px 0px" }} />
          <View className="mt-8">
            <Text as="h2">Additional service</Text>
            <PaymentSection />
          </View>
          <hr style={{ margin: "20px 0px" }} />
          <View>
            <Text as="h2">Collect Amount</Text>
            <form>
              <View className="mt-4">
                <Input
                  type="number"
                  value={amount}
                  label="Collect Amount"
                  name="collected_amount"
                  placeholder="Enter Collected Amount"
                  onChange={(e) => {
                    setAmount(Number(e.target.value));
                  }}
                  readOnly
                />
              </View>
              <View className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                {/* Mode of Payment Dropdown */}
                <View>
                  <Text className="text-sm font-medium text-gray-700 dark:text-white mb-1">
                    Mode of Payment
                  </Text>
                  <SingleSelector
                    name="payment_type"
                    options={amountTypeData?.map((item: any) => ({
                      value: item.amount_for,
                      label: item.amount_for,
                    }))}
                    placeholder="Select Payment Method"
                    value={paymentType}
                    onChange={(value) => {
                      setPaymentType(value);
                    }}
                    disabled={invoiceData?.payment_status === "Completed"}
                  />
                </View>

                {/* Transaction ID Input */}
                <View>
                  <Text className="text-sm font-medium text-gray-700 dark:text-white mb-1">
                    Transaction ID
                  </Text>
                  <Input
                    name="transaction_id"
                    placeholder="Enter Transaction ID"
                    value={transactionId}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      setTransactionId(e.target.value);
                    }}
                    disabled={invoiceData?.payment_status === "Completed"}
                  />
                </View>
              </View>
              <View className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                <View>
                  {invoiceData?.payment_status === "Completed" ? (
                    <Input
                      readOnly
                      label="Payment Status"
                      value={paymentStatus}
                    />
                  ) : (
                    <SingleSelector
                      label="Payment Status"
                      value={paymentStatus}
                      onChange={(value) => {
                        setPaymentStatus(value);
                      }}
                      placeholder="Select Appointment Payment Status"
                      options={[
                        { label: "Pending", value: "Pending" },
                        { label: "Completed", value: "Completed" },
                      ]}
                    />
                  )}
                </View>
                <View>
                  <SingleSelector
                    id="status"
                    name="status"
                    placeholder="Select Status"
                    label="Consultation Status"
                    value={status || GenericStatus.PENDING}
                    onChange={(value) => {
                      setStatus(value);
                    }}
                    options={statusOptions}
                    disabled={invoiceData?.payment_status === "Completed"}
                  />
                </View>
                <View className="col-span-2">
                  {invoiceData?.payment_status === "Completed" && (
                    <Textarea
                      required
                      value={additionalAmountReason ?? ""}
                      id="additional_amount_reason"
                      onChange={(e) => {
                        setAdditionalAmountReason(e.target.value);
                      }}
                      name="additional_amount_reason"
                      placeholder="Additional Services (after payment completed)"
                    ></Textarea>
                  )}
                </View>
              </View>
              {invoiceData?.payment_status !== "Completed" && (
                <View className=" flex justify-end mt-4">
                  <Button type="button" onClick={handleSubmit}>
                    Submit
                  </Button>
                </View>
              )}
              {additionalAmountReason && (
                <View className=" flex justify-end mt-4">
                  <Button type="button" onClick={handleSubmit}>
                    Submit
                  </Button>
                </View>
              )}
            </form>
            <Text as="h2" className="mb-2">
              Total Amount Collected: Rs {collectedAmount}
            </Text>
          </View>
        </View>
      </ServiceModel>
      {/* Footer */}
      <View className="mt-12 pt-4 border-t border-gray-300">
        <Text as="p" className="text-center text-gray-500 text-sm">
          {import.meta.env.VITE_HOSPITAL_NAME || "MedCare Hospital"}
        </Text>
      </View>
    </View>
  );
};

export default InvoiceDetail;
