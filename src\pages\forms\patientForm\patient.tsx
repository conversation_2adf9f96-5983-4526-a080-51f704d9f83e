import Text from "@/components/text";
import View from "@/components/view";
import SectionOne from "./SectionOne";
import SectionTwo from "./SectionTwo";
import SectionFour from "./SectionFour";
import Button from "@/components/button";
import SectionThree from "./SectionThree";
import { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { validationSchema } from "./validationForm";
import { usePatient } from "@/actions/calls/patient";
import { useDispatch, useSelector } from "react-redux";
import { FormTypeProps } from "@/interfaces/dashboard";
import { useNavigate, useParams } from "react-router-dom";
import { PatientInterface } from "@/interfaces/patients/index";
import { clearUserDetailsSlice } from "@/actions/slices/patient";
import { PATIENT_DETAIL_URL } from "@/utils/urls/frontend";
import useForm from "@/utils/custom-hooks/use-form";
import { imageUpload } from "@/actions/calls/uesImage";
import { LoadingStatus } from "@/interfaces";
import BouncingLoader from "@/components/BouncingLoader";

const PatientAdmissionForm: React.FC<FormTypeProps> = ({
  formType = "add",
}) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const token = useSelector((state: any) => state.authentication.tokenStatus);
  const {cleanUp, patientDetailHandler, editPatientHandler, addPatientHandler } =
    usePatient();
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch patient data on component mount for edit mode
  useEffect(() => {
    if (formType === "edit" && id) {
      patientDetailHandler(id, () => {}, [], (status: LoadingStatus) => {
        setIsLoading(status === "pending" ? true : status === "failed" ? true : status === "success" && false);
      }
      );
    }
    return () => {
      cleanUp();
      dispatch(clearUserDetailsSlice());
    };
  }, [id, formType]);
  const patientDetailData = useSelector(
    (state: any) => state.patient.patientDetailData
  );
  const patientDetail = { ...patientDetailData, id_edited: false };
  const { values: formValues, onSetHandler } =
    useForm<PatientInterface>(patientDetail);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    let patientFormObj: Partial<PatientInterface> = {};

    for (let [key, value] of formData.entries()) {
      patientFormObj[key as keyof PatientInterface] = value as any;
    }
    patientFormObj["consent"] =
      patientFormObj["consent" as keyof PatientInterface] === "true"
        ? true
        : false;
    patientFormObj["image"] = formValues?.image;
    patientFormObj["id_edited"] =
      formValues?.id_value?.includes("X") && formValues?.id_value?.includes("x")
        ? false
        : formValues?.id_edited;

    try {
      await validationSchema.validate(patientFormObj, { abortEarly: false });
      setFormErrors({});
      setIsSubmitting(true);
      delete patientFormObj["image"];
      if (formType === "add") {
        // Add new patient
        addPatientHandler(patientFormObj, (success: boolean, response: any) => {
          if (success) {
            navigate(PATIENT_DETAIL_URL + "/" + response?.data?.patient_id);
            toast({
              title: "Success!",
              description: "Patient registration submitted successfully.",
              variant: "success",
            });
            const patientId = response?.data?.id;

            // console.log("patientId", patientId);
            if (patientId && formValues?.image) {
              const imageUploadData = {
                id: patientId,
                modal_type: "patient_address_proof",
                file_name: "image",
                folder_name: "patient_address_image",
                image: formValues?.image,
              };
              imageUpload(imageUploadData, (success, _) => {
                if (success) {
                  toast({
                    title: "Success!",
                    description: "File uploaded successfully",
                    variant: "success",
                  });
                } else {
                  toast({
                    title: "Error!",
                    description: "Failed to upload File",
                    variant: "destructive",
                  });
                }
              });
            }
          } else {
            setIsSubmitting(false);
            toast({
              title: "Error!",
              description: response?.message || "Registration failed",
              variant: "destructive",
            });
          }
        });
      } else if (id) {
        // Edit existing patient
        editPatientHandler(
          id,
          patientFormObj,
          (success: boolean, response: any) => {
            if (success) {
              setIsSubmitting(false);
              toast({
                title: "Success!",
                description: "Patient updated successfully.",
                variant: "success",
              });
              const patientId = response?.data?.id;

              // console.log("patientId", patientId);
              if (patientId && formValues?.image) {
                const imageUploadData = {
                  id: patientId,
                  modal_type: "patient_address_proof",
                  file_name: "image",
                  folder_name: "patient_address_image",
                  image: formValues?.image,
                };
                imageUpload(imageUploadData, (success, _) => {
                  if (success) {
                    toast({
                      title: "Success!",
                      description: "File uploaded successfully",
                      variant: "success",
                    });
                  } else {
                    toast({
                      title: "Error!",
                      description: "Failed to upload File",
                      variant: "destructive",
                    });
                  }
                });
              }
              navigate(-1);
            } else {
              setIsSubmitting(false);
              toast({
                title: "Error!",
                description: response?.message || "Update failed",
                variant: "destructive",
              });
            }
          }
        );
      }
    } catch (err: any) {
      setIsSubmitting(false);
      if (err.inner) {
        const errors: Record<string, string> = {};
        err.inner.forEach((e: any) => {
          if (e.path) errors[e.path] = e.message;
        });
        setFormErrors(errors);

        toast({
          title: "Error!",
          description: "Something went wrong! Please check your input fields.",
          variant: "destructive",
        });
      }
    }
  };

  return (
    <>
      <BouncingLoader isLoading={isLoading} />
      <Card className="min-h-screen border-none flex flex-col justify-center items-center p-4 border-none shadow-none bg-transparent dark:bg-background dark:border-none">
        {/* Form Card */}
        <View className="bg-white dark:bg-card border border-border rounded-lg shadow-card w-full max-w-4xl p-6 md:p-8">
          <View className=" flex items-center justify-between">
            <Text
              as="h2"
              weight="font-bold"
              className="text-2xl font-bold text-center text-primary mb-2"
            >
              {formType === "add" ? "Patient Registration" : "Edit Patient"}
            </Text>
            <Button onPress={() => navigate(-1)} variant="outline">
              Back to Home
            </Button>
          </View>
          <Text
            as="p"
            className="text-text-light text-left mb-6"
            weight="font-light"
          >
            {formType === "add" &&
              "Please complete the form below to register a new patient"}
          </Text>

          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Personal Information Section */}
            <View className="space-y-4">
              <Text
                as="h3"
                className="text-lg border-b pb-2 mb-4"
                weight="font-bold"
              >
                Personal Information
              </Text>
              <SectionOne
                errorDOB={formErrors.dob}
                errorAge={formErrors.age}
                errorEmail={formErrors.email}
                errorPhoneNo={formErrors.phone_no}
                errorsGender={formErrors.gender}
                errorLastName={formErrors.last_name}
                errorFirstName={formErrors.first_name}
                errorMaritalStatus={formErrors.marital_status}
                errorsIdValue={formErrors.id_value}
                errorConsent={formErrors.consent}
                errorsIdProofForPan={formErrors.id_proof_for_pan}
                errorAttendantWithPatientName={
                  formErrors.attendant_with_patient_name
                }
                errorAttendantWithPatientPhoneNo={
                  formErrors.attendant_with_patient_phone_no
                }
                setImage={onSetHandler}
                formType={formType}
              />
            </View>

            {/* Address Information Section */}
            <View className="space-y-4">
              <Text
                as="h3"
                className="text-lg font-bold border-b pb-2 mb-4"
                weight="font-bold"
              >
                Address Information
              </Text>
              <SectionTwo
                formType={formType}
                errorsCity={formErrors.city}
                errorsState={formErrors.state}
                errorsAddress={formErrors.address}
                errorsPinCode={formErrors.pincode}
                errorsCountry={formErrors.country}
                // errorAmountFor={formErrors.amount_for}
                // errorEnroleFees={formErrors.enroll_fees}
                // errorPaymentType={formErrors.payment_type}
              />
            </View>

            {/* Medical Information Section */}
            <View className="space-y-4">
              <Text
                as="h3"
                className="text-lg font-bold border-b pb-2 mb-4"
                weight="font-bold"
              >
                Medical Information
              </Text>
              <SectionThree
                errorBloodGroup={formErrors.blood_group}
                // errorRefferedBy={formErrors.referred_by}
                // errorReferredTo={formErrors.referred_to}
                errorInsuranceProvider={formErrors.insurance_provider}
                errorInsurancePolicyNo={formErrors.insurance_policy_no}
                // errorReferredByName={formErrors.referred_by_name}
                // errorReferredByPhoneNo={formErrors.referred_by_phone_no}
                // errorReferredByEmail={formErrors.referred_by_email}
                // errorReferredByHospitalName={
                //   formErrors.referred_by_hospital_name
                // }
                // errorRefferedByPhoneNo={formErrors.referred_by_phone_no}
              />
            </View>

            {/* Status Information Section */}
            <View className="space-y-4">
              <Text
                as="h3"
                className="text-lg font-bold border-b pb-2 mb-4"
                weight="font-bold"
              >
                Status Information
              </Text>
              <SectionFour
                errorStatus={formErrors.status}
                // errorSurgeryStatus={formErrors.surgery_status}
                // errorPaymentStatus={formErrors.payment_status}
                errorReferralStatus={formErrors.referral_status}
                // errorAdmissionStatus={formErrors.admission_status}
                // errorTreatmentStatus={formErrors.treatment_status}
                // errorEmergencyStatus={formErrors.emergency_status}
              />
            </View>

            {/* Submit Button */}
            <View className="flex justify-end mt-8">
              <Button
                htmlType="submit"
                variant="primary"
                loading={isSubmitting}
                className="px-6 py-2 cursor-pointer sm:w-auto"
              >
                {isSubmitting ? "Submitting..." : "Submit"}
              </Button>
            </View>
          </form>
        </View>

        {/* Footer */}
        {!token && (
          <View className="mt-8 text-center text-text-lighter text-sm">
            <Text as="p" weight="font-light">
              © {new Date().getFullYear()} MedCare Hospital Management System.
              All rights reserved.
            </Text>
          </View>
        )}
      </Card>
    </>
  );
};

export default PatientAdmissionForm;
