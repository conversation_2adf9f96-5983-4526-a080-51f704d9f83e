import * as React from "react";
import { X } from "lucide-react";
import View from "../view";
import Button from "../button";

// ----------------------------
// Toast Variant Type
// ----------------------------
export type ToastVariant =
  | "default"
  | "destructive"
  | "success"
  | "warning"
  | "info"
  | "neutral"
  | "dark";

// ----------------------------
// Toast Events Emitter
// ----------------------------
const toastEvents = {
  listeners: new Map<string, Function[]>(),

  on(event: string, callback: (...args: any[]) => void) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
    return () => this.off(event, callback);
  },

  off(event: string, callback: (...args: any[]) => void) {
    if (!this.listeners.has(event)) return;
    const callbacks = this.listeners.get(event)!;
    const index = callbacks.indexOf(callback);
    if (index !== -1) callbacks.splice(index, 1);
  },

  emit(event: string, ...args: any[]) {
    if (!this.listeners.has(event)) return;
    this.listeners.get(event)!.forEach(callback => callback(...args));
  }
};

export { toastEvents };

// ----------------------------
// Toast Provider
// ----------------------------
export function ToastProvider({ children }: { children: React.ReactNode }) {
  return children;
}

// ----------------------------
// Toast Component
// ----------------------------
interface ToastProps extends React.HTMLAttributes<HTMLDivElement> {
  id: string;
  variant?: ToastVariant;
  className?: string;
  children: React.ReactNode;
}

export function Toast({
  className = "",
  variant = "default",
  id,
  children,
  ...props
}: ToastProps) {
  const variantClasses: Record<ToastVariant, string> = {
    default: "border bg-white text-black",
    destructive: "border-red-600 bg-red-600 text-white",
    success: "border-green-600 bg-green-50 text-green-800 border-green-200",
    warning: "border-yellow-500 bg-yellow-50 text-yellow-800 border-yellow-200",
    info: "border-blue-500 bg-blue-50 text-blue-800 border-blue-200",
    neutral: "border-gray-200 bg-gray-50 text-gray-800",
    dark: "border-gray-700 bg-gray-800 text-white"
  };

  const baseClasses =
    "group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all";

  const combinedClasses = `${baseClasses} ${variantClasses[variant]} ${className}`;

  const handleClose = () => {
    toastEvents.emit("DISMISS_TOAST", id);
  };

  return (
    <View className={combinedClasses} {...props}>
      <View className="flex-1">{children}</View>
      <ToastClose onClick={handleClose} variant={variant} />
    </View>
  );
}

// ----------------------------
// ToastViewport Component
// ----------------------------
export function ToastViewport({
  className = "",
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  const base =
    "fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]";
  const combined = `${base} ${className}`;
  return <View className={combined} {...props} />;
}

// ----------------------------
// ToastTitle Component
// ----------------------------
export function ToastTitle({
  className = "",
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return <View className={`text-sm font-semibold ${className}`} {...props} />;
}

// ----------------------------
// ToastDescription Component
// ----------------------------
export function ToastDescription({
  className = "",
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return <View className={`text-sm opacity-90 ${className}`} {...props} />;
}

// ----------------------------
// ToastClose Component
// ----------------------------
interface ToastCloseProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ToastVariant;
}

export function ToastClose({
  className = "",
  onClick,
  variant = "default",
  ...props
}: ToastCloseProps) {
  const variantClasses: Record<ToastVariant, string> = {
    default: "text-black-50 hover:text-black",
    destructive: "text-white-50 hover:text-white",
    success: "text-green-50 hover:text-green-800",
    warning: "text-yellow-50 hover:text-yellow-800",
    info: "text-blue-50 hover:text-blue-800",
    neutral: "text-gray-50 hover:text-gray-800",
    dark: "text-white-50 hover:text-white"
  };

  const baseClasses =
    "absolute right-2 top-2 rounded-md p-1 opacity-0 transition-opacity focus:opacity-100 focus:outline-none group-hover:opacity-100";

  const combined = `${baseClasses} ${variantClasses[variant]} ${className}`;

  return (
    <Button
      className={combined}
      onClick={onClick}
      aria-label="Close toast"
      type="button"
      {...props}
    >
      <X className="h-4 w-4" />
    </Button>
  );
}

// ----------------------------
// ToastAction Button
// ----------------------------
interface ToastActionProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ToastVariant;
}

export function ToastAction({
  className = "",
  variant = "default",
  ...props
}: ToastActionProps) {
  const variantClasses: Record<ToastVariant, string> = {
    default: "bg-transparent border-gray-200 hover:bg-gray-100",
    destructive: "bg-red-700 border-red-800 text-white hover:bg-red-800",
    success: "bg-green-100 border-green-200 text-green-800 hover:bg-green-200",
    warning: "bg-yellow-100 border-yellow-200 text-yellow-800 hover:bg-yellow-200",
    info: "bg-blue-100 border-blue-200 text-blue-800 hover:bg-blue-200",
    neutral: "bg-gray-100 border-gray-200 text-gray-800 hover:bg-gray-200",
    dark: "bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
  };

  const baseClasses =
    "inline-flex h-8 shrink-0 items-center justify-center rounded-md border px-3 text-sm font-medium transition-colors focus:outline-none focus:ring-2 disabled:pointer-events-none disabled:opacity-50";

  const combined = `${baseClasses} ${variantClasses[variant]} ${className}`;

  return <Button className={combined} {...props} />;
}
