# Enhanced EditableTable Component

## Overview

The EditableTable component has been upgraded to support multiple input field types while maintaining full backward compatibility. You can now use different input types like select dropdowns, checkboxes, date pickers, textareas, and custom components.

## Features

### Supported Input Types
- **text** - Standard text input (default)
- **email** - Email input with validation
- **password** - Password input
- **number** - Number input with validation
- **date** - Date picker
- **datetime-local** - Date and time picker
- **select** - Dropdown selection with options
- **checkbox** - Boolean checkbox
- **textarea** - Multi-line text input
- **custom** - Custom React component

### Key Features
- ✅ **Backward Compatible** - Existing code works without changes
- ✅ **Type Safe** - Full TypeScript support
- ✅ **Validation** - Built-in and custom validation
- ✅ **Keyboard Navigation** - Enhanced keyboard support
- ✅ **Flexible Configuration** - Per-column customization

## Usage

### Basic Usage (Backward Compatible)

```tsx
import EditableTable from './components/EditableTable';

const MyComponent = () => {
  const [tableData, setTableData] = useState([
    ['1', '<PERSON> Doe', '<EMAIL>'],
    ['2', '<PERSON>', '<EMAIL>'],
  ]);

  const tableHeaders = ['ID', 'Name', 'Email'];

  const handleCellEdit = (rowIndex: number, colIndex: number, value: any) => {
    const newData = [...tableData];
    newData[rowIndex][colIndex - 1] = value;
    setTableData(newData);
  };

  return (
    <EditableTable
      tableData={tableData}
      tableHeaders={tableHeaders}
      onCellEdit={handleCellEdit}
      editable={true}
      addRowEnabled={true}
    />
  );
};
```

### Enhanced Usage with Column Configurations

```tsx
import EditableTable, { ColumnConfig } from './components/EditableTable';

const MyEnhancedComponent = () => {
  const [tableData, setTableData] = useState([
    ['1', 'John Doe', '<EMAIL>', '2023-12-01', true, 'Manager'],
    ['2', 'Jane Smith', '<EMAIL>', '2023-11-15', false, 'Developer'],
  ]);

  const tableHeaders = ['ID', 'Name', 'Email', 'Join Date', 'Active', 'Role'];

  const columnConfigs: ColumnConfig[] = [
    {
      header: 'ID',
      type: 'number',
      placeholder: 'Enter ID',
      required: true,
    },
    {
      header: 'Name',
      type: 'text',
      placeholder: 'Enter full name',
      required: true,
      validation: (value) => {
        if (value && value.length < 2) {
          return 'Name must be at least 2 characters long';
        }
        return null;
      },
    },
    {
      header: 'Email',
      type: 'email',
      placeholder: 'Enter email address',
      required: true,
    },
    {
      header: 'Join Date',
      type: 'date',
      placeholder: 'Select join date',
      required: true,
    },
    {
      header: 'Active',
      type: 'checkbox',
    },
    {
      header: 'Role',
      type: 'select',
      placeholder: 'Select role',
      required: true,
      options: [
        { value: 'Manager', label: 'Manager' },
        { value: 'Developer', label: 'Developer' },
        { value: 'Designer', label: 'Designer' },
      ],
    },
  ];

  return (
    <EditableTable
      tableData={tableData}
      tableHeaders={tableHeaders}
      columnConfigs={columnConfigs}
      onCellEdit={handleCellEdit}
      editable={true}
      addRowEnabled={true}
    />
  );
};
```

## Column Configuration Options

```tsx
interface ColumnConfig {
  header: string;                    // Column header text
  type: ColumnType;                  // Input type
  key?: string;                      // Optional data mapping key
  placeholder?: string;              // Placeholder text
  required?: boolean;                // Required field validation
  disabled?: boolean;                // Disable editing
  options?: SelectOption[];          // Options for select type
  validation?: (value: any) => string | null; // Custom validation
  customComponent?: React.ComponentType<...>; // Custom component
  props?: Record<string, any>;       // Additional props
}
```

## Keyboard Navigation

- **Enter** - Save current edit and move to next field
- **Escape** - Cancel current edit
- **Ctrl+Enter** - Save textarea content (allows Enter for new lines)
- **Tab** - Navigate between fields (standard browser behavior)

## Migration Guide

### From Basic to Enhanced

1. **No changes required** for existing implementations
2. **Optional**: Add `columnConfigs` prop for enhanced features
3. **Optional**: Update data handling for new types (checkbox, etc.)

### Example Migration

```tsx
// Before (still works)
<EditableTable
  tableData={data}
  tableHeaders={headers}
  onCellEdit={handleEdit}
/>

// After (enhanced)
<EditableTable
  tableData={data}
  tableHeaders={headers}
  columnConfigs={configs}  // Add this for new features
  onCellEdit={handleEdit}
/>
```

## Custom Components

You can create custom input components:

```tsx
const CustomDatePicker = ({ value, onChange, ...props }) => {
  return (
    <MyCustomDatePicker
      value={value}
      onChange={onChange}
      {...props}
    />
  );
};

const columnConfig = {
  header: 'Custom Date',
  type: 'custom',
  customComponent: CustomDatePicker,
};
```

## Validation

Built-in validation for email and number types, plus custom validation:

```tsx
const columnConfig = {
  header: 'Age',
  type: 'number',
  validation: (value) => {
    if (value < 0 || value > 120) {
      return 'Age must be between 0 and 120';
    }
    return null;
  },
};
```
