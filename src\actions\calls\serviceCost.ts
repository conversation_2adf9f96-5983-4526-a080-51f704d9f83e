import LaunchApi from "../api";
import { useDispatch } from "react-redux";
import { ApiCallback } from "@/interfaces/api";
import { AuthPayload } from "@/interfaces/slices/auth";
import {
  SERVICE_COST_ADD_URL,
  SERVICE_COST_DELETE_URL,
  SERVICE_COST_DETAILS_URL,
  SERVICE_COST_EDIT_URL,
  SERVICE_COST_LIST_URL,
  SERVICE_COST_DROPDOWN_URL,
} from "@/utils/urls/backend";
import {
  serviceCostDetailSlice,
  serviceCostListSlice,
  serviceCostDropdownSlice,
  totalServiceCostSlice,
} from "@/actions/slices/serviceCost";
import { LoadingStatus } from "@/interfaces";

const api = new LaunchApi();

export const useServiceCost = () => {
  const dispatch = useDispatch();

  const serviceCostDetailHandler = async (
    id: string,
    callback: ApiCallback,
    data?: any,
    isLoading?: (status: LoadingStatus) => void
  ): Promise<void> => {
    try {
      await api.get(
        SERVICE_COST_DETAILS_URL + "/" + id,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(serviceCostDetailSlice(response.data));
            return callback(true);
          } else {
            return callback(false);
          }
        },
        data,
        (status) => {
          isLoading?.(status);
        }
      );
    } catch (error) {
      callback(false);
    }
  };

  const serviceCostListHandler = async (
    page: number | string = 1,
    callback: ApiCallback,
    search?: string | null,
    sort_by?: string | null,
    sort_order?: string | null,
    data?: any,
    isLoading?: (status: LoadingStatus) => void
  ): Promise<void> => {
    try {
      await api.get(
        `${SERVICE_COST_LIST_URL}?page=${page}${
          search ? "&search=" + search : ""
        }${sort_by ? "&sort_by=" + sort_by : ""}${
          sort_order ? "&sort_order=" + sort_order : ""
        }`,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(serviceCostListSlice(response.data));
            return callback(true);
          } else {
            return callback(false);
          }
        },
        data,
        (status) => {
          isLoading?.(status);
        }
      );
    } catch (error) {
      callback(false);
    }
  };

  const addServiceCostHandler = async (
    data: any,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.post(
        SERVICE_COST_ADD_URL,
        (_: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            return callback(true);
          } else {
            return callback(false);
          }
        },
        data
      );
    } catch (error) {
      callback(false);
    }
  };

  const editServiceCostHandler = async (
    id: string,
    data: any,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.put(
        SERVICE_COST_EDIT_URL + "/" + id,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            return callback(true, response.data);
          } else {
            return callback(false);
          }
        },
        data
      );
    } catch (error) {
      callback(false);
    }
  };

  const deleteServiceCostHandler = async (
    id: string,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.delete(
        SERVICE_COST_DELETE_URL,
        id,
        (_: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            return callback(true);
          } else {
            return callback(false);
          }
        }
      );
    } catch (error) {
      callback(false);
    }
  };

  const serviceCostDropdownHandler = async (
    callback: ApiCallback,
    departmentValue?: string
  ): Promise<void> => {
    try {
      await api.get(
        `${SERVICE_COST_DROPDOWN_URL}/${departmentValue ?? "All"}`,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(serviceCostDropdownSlice(response.data));
            return callback(true);
          } else {
            return callback(false);
          }
        }
      );
    } catch (error) {
      callback(false);
    }
  };

  const setTotalServiceCost = (totalCost: number) => {
    dispatch(totalServiceCostSlice(totalCost));
  };

  const cleanUp = () => {
    api.cleanup();
  };

  return {
    cleanUp,
    addServiceCostHandler,
    serviceCostListHandler,
    serviceCostDetailHandler,
    editServiceCostHandler,
    deleteServiceCostHandler,
    serviceCostDropdownHandler,
    setTotalServiceCost,
  };
};
