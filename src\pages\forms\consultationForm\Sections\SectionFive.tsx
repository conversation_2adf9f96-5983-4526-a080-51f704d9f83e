import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import WebcamCapture from "@/components/Capture";
import useForm from "@/utils/custom-hooks/use-form";
import { Consultation } from "@/interfaces/consultation";
// import dayjs from "dayjs";
// import { useEffect } from "react";
// import View from "@/components/view";
// import Text from "@/components/text";
// import Text from "@/components/text";
// import Input from "@/components/input";
// import Button from "@/components/button";
// import Select from "@/components/Select";
// import { Card } from "@/components/ui/card";
// import Textarea from "@/components/Textarea";
// import { useOpd } from "@/actions/calls/opd";
// import { useTest } from "@/actions/calls/test";
// import WebcamCapture from "@/components/Capture";
// import SearchSelect from "@/components/SearchSelect";
// import SearchSelect from "@/components/SearchSelect";
// import TransferList from "@/components/TransferList";
// import { useMedicine } from "@/actions/calls/medicine";
// import MultiSelector from "@/components/MultiSelector";
// import { Appointment } from "@/interfaces/appointments";
// import SingleSelector from "@/components/SingleSelector";
// import { statusOptions } from "../consultationFormOptions";
// import TipTapTextEditor from "@/components/TipTapTexteditor";
// import MedicinesSection from "@/components/MedicinesSection";
// import DynamicFormSection from "@/components/DynamicFormSection";
// import MultiSelectWithDropDown from "@/components/MultiSelectWithDropDown";

interface SectionFourProps {
  // errorsTemperature: string;
  // errorsBp: string;
  // errorsPulse: string;
  // errorsCvs: string;
  // errorsRs: string;
  // errorsTest: string;
  // postExaminationData: any;
  mainOnSetHandler: (name: string, value: any) => void;
}

const SectionFive: React.FC<SectionFourProps> = ({
  // errorsTemperature,
  // errorsBp,
  // errorsPulse,
  // errorsCvs,
  // errorsRs,
  // errorsTest,
  // postExaminationData,
  mainOnSetHandler,
}) => {
  // const examinationDetails = useSelector(
  //   (state: RootState) => state.examinations.examinationDetails
  // );
  // const { values, handleChange } = useForm<Examination | null>(
  //   examinationDetails
  // );
  // const { medicineDropdownHandler } = useMedicine();

  const consultationDetail = useSelector(
    (state: RootState) =>
      state.consultation.consultationDetailData?.proctologyOrNonProctology
  );

  // const medicineDropdownData = useSelector(
  //     (state: RootState) => state.medicines.medicineDropdownData
  //   )?.map((item: any) => ({
  //     id: item?.id,
  //     label: item?.medicine_name,
  //     value: item?.medicine_name,
  //   }));

  //   useEffect(() => {
  //       medicineDropdownHandler(() => {});
  //     }, []);

  // const testIds = testData?.split(",")?.map((item: any) => item.trim());
  // const testLabelMap = testObj?.filter((item: any) =>
  //   testIds?.includes(item?.value?.toString())
  // )?.map((item: any) => {
  //   return {
  //     id: item?.value,
  //     label: item?.label,
  //     value: item?.value,
  //   };
  // });
  // const testLabelMap = testObj?.filter((item: any) =>
  //   testIds?.includes(item?.value?.toString())
  // )?.map((item: any) => item?.label)?.join(",");
  // console.log("testLabelMap", testLabelMap);

  const { values ,onSetHandler } = useForm<Consultation | null>(consultationDetail);

  return (
    <React.Fragment>
       <WebcamCapture 
          name="doc_upload"
          // onChange={(event: any) => {
          //   onSetHandler("doc_upload", event?.target?.files[0]);
          // }}
          accept="image/*,.pdf,.doc,.docx,.txt,.mp4,.mov,.mkv,.webm,.webp"
          multiple
          maxSize={1024 * 1024 * 15}
          existingFiles={ 
            typeof values?.doc_upload === "string"
              ? values?.doc_upload
              : Array.isArray(values?.doc_upload) &&
                values?.doc_upload.length > 0
              ? values?.doc_upload
                  .filter((item) => typeof item === "string")
                  .join(",")
              : ""
          }
          label="Upload Documents & Files (Max 15MB)"
          onChange={(fileList: any) => {
            // Separate existing URLs and new files
            const existingUrls: string[] = [];
            const newFiles: File[] = [];

            fileList?.forEach((item: any) => {
              if (item.isExisting && item.url) {
                existingUrls.push(item.url);
              } else if (
                !item.isExisting &&
                item.file &&
                item.file instanceof File
              ) {
                newFiles.push(item.file);
              }
            });

            // Store URLs and Files separately to avoid serialization issues
            const urlsString = existingUrls.join(",");

            // Store in local form (for this component)
            onSetHandler("existing_file_urls", urlsString);
            onSetHandler("new_files_count", newFiles.length);

            // IMPORTANT: Store in main consultation form (for submission)
            if (mainOnSetHandler) {
              mainOnSetHandler("existing_file_urls", urlsString);
              mainOnSetHandler("new_files", newFiles);

              // Also store combined for backward compatibility
              const combinedFiles = [...existingUrls, ...newFiles];
              mainOnSetHandler("doc_upload", combinedFiles);
            }
          }}
          // value={values?.consultation_image}
        />

    </React.Fragment>
  );
};
export default SectionFive;
