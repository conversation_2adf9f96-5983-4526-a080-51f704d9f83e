import React, { useState } from 'react';
import EditableTable, { ColumnConfig } from './EditableTable';
import dayjs from 'dayjs';

// Test component to verify the Post Surgery Follow-up table implementation
const PostSurgeryTableTest: React.FC = () => {
  // Sample data structure matching the ConsultationDetail implementation
  const [data, setData] = useState([
    {
      id: 1,
      post_surgery_name: 'Fistulotomy',
      date: '2023-12-01',
      ks_changed: true,
      dressing: 'yes',
      partial_lay_open: false,
      follow_up_examination: 'Patient healing well, no complications observed.',
      new_abscess_threading: false,
      new_tract_primary_threading: true,
      cut_through: 'Minor bleeding noted, managed conservatively.',
    },
    {
      id: 2,
      post_surgery_name: 'Sphincterotomy',
      date: '2023-11-15',
      ks_changed: false,
      dressing: 'partial',
      partial_lay_open: true,
      follow_up_examination: 'Good progress, continue current treatment.',
      new_abscess_threading: true,
      new_tract_primary_threading: false,
      cut_through: '',
    },
  ]);

  const tableHeaders = [
    { label: "Post Surgery", key: "post_surgery_name" },
    { label: "ID", key: "id" },
    { label: "Date", key: "date" },
    { label: "KS Changed", key: "ks_changed" },
    { label: "Dressing", key: "dressing" },
    { label: "Partial Lay Open", key: "partial_lay_open" },
    { label: "Follow-up Examination", key: "follow_up_examination" },
    { label: "New Abscess I&D", key: "new_abscess_threading" },
    { label: "New Tract Primary Threading", key: "new_tract_primary_threading" },
    { label: "Cut Through or Any Other", key: "cut_through" },
  ];

  // Sample post surgery dropdown data
  const postSurgeryDropdown = [
    { value: 'Fistulotomy', label: 'Fistulotomy' },
    { value: 'Sphincterotomy', label: 'Sphincterotomy' },
    { value: 'Hemorrhoidectomy', label: 'Hemorrhoidectomy' },
    { value: 'Pilonidal Sinus Surgery', label: 'Pilonidal Sinus Surgery' },
  ];

  // Enhanced column configurations
  const columnConfigs: ColumnConfig[] = [
    {
      header: "Post Surgery",
      type: "select",
      placeholder: "Select post surgery type",
      required: true,
      options: postSurgeryDropdown,
    },
    {
      header: "ID",
      type: "number",
      placeholder: "ID",
      disabled: true,
    },
    {
      header: "Date",
      type: "date",
      placeholder: "Select date",
      required: true,
    },
    {
      header: "KS Changed",
      type: "checkbox",
    },
    {
      header: "Dressing",
      type: "select",
      placeholder: "Select dressing type",
      options: [
        { value: "yes", label: "Yes" },
        { value: "no", label: "No" },
        { value: "partial", label: "Partial" },
      ],
    },
    {
      header: "Partial Lay Open",
      type: "checkbox",
    },
    {
      header: "Follow-up Examination",
      type: "textarea",
      placeholder: "Enter examination details",
    },
    {
      header: "New Abscess I&D",
      type: "checkbox",
    },
    {
      header: "New Tract Primary Threading",
      type: "checkbox",
    },
    {
      header: "Cut Through or Any Other",
      type: "textarea",
      placeholder: "Enter additional details",
    },
  ];

  const handleCellEdit = (rowIndex: number, colIndex: number, value: any) => {
    const adjustedColIndex = colIndex - 1;
    const key = tableHeaders[adjustedColIndex]?.key;
    
    if (key) {
      setData((prev) => {
        const newData = [...prev];
        newData[rowIndex] = { ...newData[rowIndex], [key]: value };
        return newData;
      });
    }
  };

  const handleRowAdd = (newRow: any[]) => {
    const newObj: Record<string, any> = {};
    tableHeaders.forEach((header, index) => {
      let value = newRow[index];
      
      const columnConfig = columnConfigs[index];
      if (columnConfig?.type === 'checkbox') {
        value = Boolean(value);
      } else if (columnConfig?.type === 'date' && value) {
        value = dayjs(value).format("YYYY-MM-DD");
      }
      
      newObj[header.key] = value || (columnConfig?.type === 'checkbox' ? false : "");
    });

    // Generate new ID
    newObj.id = Math.max(...data.map(d => d.id), 0) + 1;
    setData(prev => [...prev, newObj]);
  };

  const handleRowDelete = (rowIndex: number) => {
    setData(prev => prev.filter((_, index) => index !== rowIndex));
  };

  const handleSubmitCompleteRow = (rowData: any) => {
    console.log('Submitting row:', rowData);
    
    const result = tableHeaders.reduce((acc, h, index) => {
      let value = rowData[index];
      
      const columnConfig = columnConfigs[index];
      if (columnConfig?.type === 'checkbox') {
        value = Boolean(value);
      } else if (columnConfig?.type === 'date' && value) {
        value = dayjs(value).format("YYYY-MM-DD");
      } else if (columnConfig?.type === 'select' && columnConfig.options) {
        const option = columnConfig.options.find(opt => opt.label === value || opt.value === value);
        value = option ? option.value : value;
      }
      
      acc[h.key] = value;
      return acc;
    }, {} as Record<string, any>);
    
    console.log('Processed result:', result);
  };

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-4">Post Surgery Follow-up Table Test</h2>
      <p className="text-gray-600 mb-4">
        This demonstrates the enhanced EditableTable with different input types for the Post Surgery Follow-up form.
      </p>
      
      <EditableTable
        tableHeaders={tableHeaders.map((h) => h.label)}
        columnConfigs={columnConfigs}
        tableData={data.map((row) =>
          tableHeaders.map((h) => row[h.key as keyof typeof row])
        )}
        onCellEdit={handleCellEdit}
        onRowAdd={handleRowAdd}
        onRowDelete={handleRowDelete}
        editable={true}
        addRowEnabled={true}
        deleteRowEnabled={true}
        onSubmitCompleteRow={handleSubmitCompleteRow}
      />
      
      <div className="mt-6">
        <h3 className="text-lg font-semibold mb-2">Current Data:</h3>
        <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
          {JSON.stringify(data, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default PostSurgeryTableTest;
