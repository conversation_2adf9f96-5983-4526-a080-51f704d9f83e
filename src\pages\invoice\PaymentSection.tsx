import View from "@/components/view";
import Input from "@/components/input";
import Button from "@/components/button";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { useParams } from "react-router-dom";
import { validationForm } from "./validationForm";
import React, { useEffect, useState } from "react";
import { useInvoice } from "@/actions/calls/invoice";
import { toast } from "@/utils/custom-hooks/use-toast";
import SingleSelector from "@/components/SingleSelector";
import { useServiceCost } from "@/actions/calls/serviceCost";
import { setServicesModel } from "@/actions/slices/medicalStatus";

// import Select from "@/components/Select";
// import { amountOptions } from "../forms/appointmentsForm/appointmentFormOptions";

const PaymentSection: React.FC<{}> = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const [amount, setAmount] = useState<string>("");
  const [amountFor, setAmountFor] = useState<string>("");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const { invoicePayment, getPaymentDetailHandler, getInvoiceDetailHandler } =
    useInvoice();
  const { serviceCostDropdownHandler } = useServiceCost();
  const invoiceData = useSelector(
    (state: RootState) => state.invoice.invoiceDetailData
  );

  const discountPercentage = useSelector(
    (state: RootState) => state.invoice.paymentDetailData
  )[0]?.discount_percentage;

  const serviceCostDropdownData = useSelector(
    (state: RootState) => state.serviceCost.serviceCostDropdownData
  )?.map((data) => {
    return {
      id: data?.id,
      label: data?.service_name,
      value: data?.service_name + "#" + data?.cost,
    };
  });

  useEffect(() => {
    serviceCostDropdownHandler(() => {});
  }, []);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const data: any = {};
    try {
      for (let [key, value] of formData.entries()) {
        // console.log(key, value);
        data[key as keyof any] = value as any;
      }
      // console.log(formData.entries());

      await validationForm.validate(data, { abortEarly: false });
      setErrors({});
      data["patient_id"] = invoiceData?.patient_id;
      data["appointment_id"] = invoiceData?.appointment_id;
      data["consultation_id"] = invoiceData?.id;
      data["front_desk_user_id"] = invoiceData?.front_desk_user_id;
      data["doctor_id"] = invoiceData?.doctor_id;
      data["discount_percentage"] = discountPercentage || 0;

      invoicePayment(data, (success: boolean) => {
        if (success && id) {
          setAmount("");
          setAmountFor("");
          getPaymentDetailHandler(id, () => {});
          getInvoiceDetailHandler(id, () => {});
          toast({
            title: "Success!",
            description: "Successfully payment added",
            variant: "success",
          });
        } else {
          toast({
            title: "Error",
            description: "Failed to payment added",
            variant: "destructive",
          });
        }
      });
    } catch (e) {
      console.error(e);
    }
  };
  return (
    <React.Fragment>
      <form onSubmit={handleSubmit}>
        <View className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
          {/* <View>
            <Input
              required={true}
              id="amount"
              name="amount"
              value={amount}
              error={errors?.amount}
              label="Amount"
              placeholder="Enter Amount"
              onChange={(e) => {
                setAmount(e.target.value);
              }}
            />
          </View>
          <View>
            <Select
              id="amount_for"
              required={true}
              name="amount_for"
              value={amountFor}
              label="Amount For"
              options={amountOptions}
              error={errors?.amount_for}
              placeholder="Select Amount For"
              onChange={(e) => {
                setAmountFor(e.target.value);
              }}
            />
          </View> */}
          {/* Dropdown Selector */}
          <View className="w-full">
            <SingleSelector
              label="Amount For"
              name="amount_for"
              options={serviceCostDropdownData}
              placeholder={`Select`}
              value={amountFor}
              error={errors?.amount_for}
              onChange={(value) => {
                setAmountFor(value.split("#")[0]);
                setAmount(value.split("#")[1]);
              }}
            />
          </View>

          {/* Cost Input (Read-only) */}
          <View className="w-full">
            <Input
              label="Amount"
              name="amount"
              value={amount}
              error={errors?.amount}
              readOnly
              type="number"
              onChange={(e) => {
                setAmount(e.target.value);
              }}
              // placeholder={item.input.placeholder}
            />
          </View>
        </View>
        <Button
          variant="secondary"
          className="w-full mt-4"
          onPress={() => {
            dispatch(setServicesModel(true));
          }}
        >
          <span className="text-primary">Add Service Amount</span>
        </Button>
        <View className="flex justify-end my-4">
          <Button type="submit">Submit</Button>
        </View>
      </form>
    </React.Fragment>
  );
};

export default PaymentSection;
