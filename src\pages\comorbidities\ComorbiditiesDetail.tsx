import { useNavigate, useParams } from "react-router-dom";
import {
  Activity,
  Calendar,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import But<PERSON> from "@/components/button";
import { useDispatch, useSelector } from "react-redux";
import { useComorbidity } from "@/actions/calls/comorbidities";
import { useEffect, useState } from "react";
import BouncingLoader from "@/components/BouncingLoader";
import { clearComorbiditySlice } from "@/actions/slices/comorbidities";
import View from "@/components/view";
import Text from "@/components/text";

const ComorbidityDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const dispatch = useDispatch();
  const { comorbidityDetail, cleanUp } = useComorbidity();
  const comorbidityData = useSelector(
    (state: any) => state.comorbidities.comorbidityDetails
  );
  console.log("comorbidityData", comorbidityData);
  
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (id) {
      comorbidityDetail(id, () => {}, [], (status) => {
        setIsLoading(status === "pending" ? true : status === "failed" ? true : status === "success" && false);
      }
      );
    }
    return () => {
      cleanUp();
      dispatch(clearComorbiditySlice());
    };
  }, [id]);

  // In a real app, you'd fetch data based on the ID
  // const getStatusBadge = (status: string) => {
  //   const isActive = status.toLowerCase() === "active";
  //   return (
  //     <Badge
  //       variant={isActive ? "default" : "secondary"}
  //       className="font-medium"
  //     >
  //       {isActive ? (
  //         <CheckCircle className="w-3 h-3 mr-1" />
  //       ) : (
  //         <Clock className="w-3 h-3 mr-1" />
  //       )}
  //       {status}
  //     </Badge>
  //   );
  // };

  // const getChronicBadge = (isChronic: number) => {
  //   const chronic = isChronic === 1;
  //   return (
  //     <Badge
  //       variant={chronic ? "destructive" : "outline"}
  //       className="font-medium"
  //     >
  //       {chronic ? (
  //         <AlertCircle className="w-3 h-3 mr-1" />
  //       ) : (
  //         <Activity className="w-3 h-3 mr-1" />
  //       )}
  //       {chronic ? "Chronic" : "Acute"}
  //     </Badge>
  //   );
  // };

  return (
    <View className="min-h-screen bg-primary-10 dark:bg-background p-4">
      <BouncingLoader isLoading={isLoading} />
      <View className="max-w-4xl mx-auto">
        {/* Header */}
        <View className="mb-6 flex items-center justify-between">
          <Text as="h1" weight="font-semibold" className="text-2xl ">
              Comorbidity Details
            </Text>
          <View className="flex justify-end mb-4">
            
            <Button
              className="flex items-center gap-2"
              variant="outline"
              size="small"
              onClick={() => navigate(-1)}
            >
              Back to Home
            </Button>
          </View>

         
        </View>

        {/* Main Content */}
        <View className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information Card */}
          <Card className="bg-card/80 backdrop-blur-sm shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-foreground">
                <Activity className="w-5 h-5 mr-2 text-primary" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <View className="bg-background rounded-lg p-4 ">
                <View className="text-sm font-medium text-primary mb-1 block">
                  Condition Name
                </View>
                <Text as="p" className="text-lg font-semibold text-foreground">
                  {comorbidityData?.name || "N/A"}
                </Text>
              </View>

              <View className="bg-background rounded-lg p-4">
                <Text as="label" className="text-sm font-medium text-muted-foreground mb-1 block">
                  Description
                </Text>
                <Text className="text-foreground leading-relaxed">
                  {comorbidityData?.description || "No description available"}
                </Text>
              </View>
            </CardContent>
          </Card>

          {/* Status Information Card */}
          <Card className="bg-card/80 backdrop-blur-sm shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-foreground">
                <Calendar className="w-5 h-5 mr-2 text-secondary-foreground" />
                Status Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <View className="bg-background rounded-lg p-4 border border-border">
                <Text as="label" className="text-sm font-medium text-accent-foreground mb-2 block">
                  Current Status
                </Text>
                <View>
                  <Text as="span" className={`${comorbidityData?.is_active === "Active" ? "text-green-600" : "text-yellow-600"}`}>
                    {comorbidityData?.is_active || "N/A"}
                  </Text>
               
                </View>
                
              </View>

              <View className="bg-background rounded-lg p-4 border border-border">
                <Text as="label" className="text-sm font-medium text-secondary-foreground mb-2 block">
                  Is Chronic?
                </Text>
                <View className="flex items-center">
                  <Text as="span" className={`${comorbidityData?.is_chronic === 1 ? "text-red-600" : "text-green-600"}`}>
                    {comorbidityData?.is_chronic === 1 ? "Yes" : "No"}
                  </Text>
                </View>
           
              </View>
            </CardContent>
          </Card>
        </View>

        {/* Summary Card */}
        {/* <Card className="mt-6 bg-card/80 backdrop-blur-sm shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center text-foreground">
              <AlertCircle className="w-5 h-5 mr-2 text-primary" />
              Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gradient-to-r from-muted/50 to-accent/30 rounded-lg p-6 border">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div className="p-4">
                  <div className="text-2xl font-bold text-primary mb-1">
                    {comorbidityData?.name}
                  </div>
                  <div className="text-sm text-muted-foreground">Condition</div>
                </div>

                <div className="p-4">
                  <div className="text-2xl font-bold text-secondary-foreground mb-1">
                    {comorbidityData?.is_active}
                  </div>
                  <div className="text-sm text-muted-foreground">Status</div>
                </div>

                <div className="p-4">
                  <div className="text-2xl font-bold text-accent-foreground mb-1">
                    {comorbidityData?.is_chronic === 1 ? "Chronic" : "Acute"}
                  </div>
                  <div className="text-sm text-muted-foreground">Type</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card> */}
      </View>
    </View>
  );
};

export default ComorbidityDetail;
