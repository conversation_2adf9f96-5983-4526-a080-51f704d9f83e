import View from "@/components/view";
import Input from "@/components/input";
import CostSummary from "./CostSummary";
import { RootState } from "@/actions/store";
import { GenericStatus } from "@/interfaces";
import AddCostInput from "@/components/AddCost";
import React, { useEffect, useState } from "react";
import useForm from "@/utils/custom-hooks/use-form";
import { useDispatch, useSelector } from "react-redux";
import SingleSelector from "@/components/SingleSelector";
import { Consultation } from "@/interfaces/consultation";
import { statusOptions } from "../consultationFormOptions";
import { useServiceCost } from "@/actions/calls/serviceCost";
import { discountPercentSlice } from "@/actions/slices/serviceCost";
import { setConsultationAmount } from "@/actions/slices/consultation";
import { useConsultationFees } from "@/actions/calls/consultationFees";
// import dayjs from "dayjs";
// import Text from "@/components/text";
// import Text from "@/components/text";
// import Text from "@/components/text";
// import Input from "@/components/input";
// import Select from "@/components/Select";
// import Button from "@/components/button";
// import { Card } from "@/components/ui/card";
// import Textarea from "@/components/Textarea";
// import { useOpd } from "@/actions/calls/opd";
// import { useTest } from "@/actions/calls/test";
// import SearchSelect from "@/components/SearchSelect";
// import TransferList from "@/components/TransferList";
// import SearchSelect from "@/components/SearchSelect";
// import MultiSelector from "@/components/MultiSelector";
// import { useMedicine } from "@/actions/calls/medicine";
// import { Appointment } from "@/interfaces/appointments";
// import TipTapTextEditor from "@/components/TipTapTexteditor";
// import MedicinesSection from "@/components/MedicinesSection";
// import useExtractValue from "@/utils/custom-hooks/useExtractValue";
// import MultiSelectWithDropDown from "@/components/MultiSelectWithDropDown";

interface SectionFourProps {
  // errorsTemperature: string;
  // errorsBp: string;
  // errorsPulse: string;
  // errorsCvs: string;
  // errorsRs: string;
  // errorsTest: string;
  // postExaminationData: any;
  // mainOnSetHandler: (name: string, value: any) => void;
}

const SectionSix: React.FC<SectionFourProps> = (
  {
    // errorsTemperature,
    // errorsBp,
    // errorsPulse,
    // errorsCvs,
    // errorsRs,
    // errorsTest,
    // postExaminationData,
    // mainOnSetHandler,
  }
) => {
  // const examinationDetails = useSelector(
  //   (state: RootState) => state.examinations.examinationDetails
  // );
  // const { values, handleChange } = useForm<Examination | null>(
  //   examinationDetails
  // );
  // const { medicineDropdownHandler } = useMedicine();

  const { consultationFeesDropdownHandler } = useConsultationFees();

  // const totalServiceCost = useExtractValue("serviceCost", "totalServiceCost");
  // console.log("totalServiceCost", totalServiceCost);

  // const totalServiceCost = useSelector(
  //   (state: RootState) => state.serviceCost.totalServiceCost
  // );

  // const currencySymbol = useExtractValue("systemSettings", "settings.currency_symbol");
  // const currencySymbol = useSelector(
  //   (state: RootState) => state.systemSettings.settings.currency_symbol
  // );

  // const consultationDetail = useExtractValue("consultation", "consultationDetailData");
  const consultationDetail = useSelector(
    (state: RootState) => state.consultation.consultationDetailData
  );
  const discountPercent = useSelector(
    (state: RootState) => state.serviceCost.discountPercent
  );
  const consultationDetailData = {
    ...consultationDetail?.consultations,
    ...consultationDetail?.proctologyOrNonProctology,
    // additional_cost: consultationDetail?.proctologyOrNonProctology?.additional_cost,
  };

  const { values, handleChange, onSetHandler } = useForm<Consultation | null>(
    consultationDetailData || {}
  );

  // const medicineDropdownData = useSelector(
  //     (state: RootState) => state.medicines.medicineDropdownData
  //   )?.map((item: any) => ({
  //     id: item?.id,
  //     label: item?.medicine_name,
  //     value: item?.medicine_name,
  //   }));

  //   useEffect(() => {
  //       medicineDropdownHandler(() => {});
  //     }, []);

  // const testIds = testData?.split(",")?.map((item: any) => item.trim());
  // const testLabelMap = testObj?.filter((item: any) =>
  //   testIds?.includes(item?.value?.toString())
  // )?.map((item: any) => {
  //   return {
  //     id: item?.value,
  //     label: item?.label,
  //     value: item?.value,
  //   };
  // });
  // const testLabelMap = testObj?.filter((item: any) =>
  //   testIds?.includes(item?.value?.toString())
  // )?.map((item: any) => item?.label)?.join(",");
  // console.log("testLabelMap", testLabelMap);

  const { serviceCostDropdownHandler } = useServiceCost();
  const [, setServiceCostStatus] = useState<boolean>(false);
  const dispatch = useDispatch();
  const serviceCostDropdownData = useSelector(
    (state: RootState) => state.serviceCost.serviceCostDropdownData
  )?.map((data) => {
    return {
      id: data?.id,
      label: data?.service_name,
      value: data?.service_name + "#" + data?.cost,
    };
  });

  const consultationFeesDropdownData = useSelector(
    (state: RootState) => state.consultationFees.consultationFeesDropdownData
  )?.map((data) => {
    return {
      id: data?.id,
      label: data?.amount,
      value: data?.amount,
    };
  });

  // const { values, handleChange, handleTipTapChange, onSetHandler } =
  //   useForm<Consultation | null>(consultationDetail);

  useEffect(() => {
    if (consultationDetailData?.type) {
      consultationFeesDropdownHandler(() => {}, consultationDetailData?.type);
      serviceCostDropdownHandler(() => {
        setServiceCostStatus(true);
      }, consultationDetailData?.type);
    }
    // consultationFeesDropdownHandler(() => {});
  }, [consultationDetailData?.type]);

  useEffect(() => {
    dispatch(discountPercentSlice(values?.consultation_discount));
  }, [values?.consultation_discount]);

  return (
    <React.Fragment>
      <View className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* consultatioin type  */}
        <View>
          <Input
            id="type"
            name="type"
            label="Consultation Type"
            value={values?.type || ""}
            onChange={handleChange}
            placeholder="Enter Consultation Type"
            required={true}
            readOnly={true}
          />
          {/* <SingleSelector
            id="type"
            label="Consultation Type"
            name="type"
            value={values?.type || ""}
            placeholder="Select Consultation Type"
            onChange={(value) => {
              onSetHandler("type", value);
            }}
            options={[
              { label: "Proctology", value: "Proctology" },
              { label: "Non Proctology", value: "Non Proctology" },
              { label: "Allopathy", value: "Allopathy" },
            ]}
            required={true}
            
          /> */}
        </View>

        {/* consultaton cost  */}
        <View>
          <SingleSelector
            id="consultation_amount"
            label="Consultation Cost"
            name="consultation_amount"
            value={values?.consultation_amount?.toString() || ""}
            placeholder="Select Consultation Cost"
            onChange={(value) => {
              dispatch(
                setConsultationAmount({
                  amount: Number(value),
                })
              );
              onSetHandler("consultation_amount", value);
            }}
            options={consultationFeesDropdownData}
            // required={true}
          />
        </View>
      </View>

      <View>
        {/* <DynamicFormSection
          title="Addition Costs"
          itemLabelPrefix="Addition cost"
          addButtonText="Add Cost"
          onPressed={() => {
            if (!serviceCostStatus) {
              serviceCostDropdownHandler(() => {
                setServiceCostStatus(true);
              });
            }
          }}
          data={consultationDetail?.proctologyOrNonProctology?.additional_cost?.split(",")}
          fieldConfigs={[
            {
              key: "Service",
              label: "Service#Cost",
              type: "custom-select",
              placeholder: "Select Service",
              required: true,
              // value: values?.additional_cost.split(","),
              options: serviceCostDropdownData,
              // options: [
              //   { label: "Test", value: "Test" },
              //   { label: "Medicine", value: "Medicine" },
              //   { label: "Surgery", value: "Surgery" },
              //   { label: "Consultation", value: "Consultation" },
              //   { label: "Admission", value: "Admission" },
              //   { label: "Discharge", value: "Discharge" },
              // ],
            },
            // {
            //   key: "Cost",
            //   label: "Cost",
            //   type: "number",
            //   placeholder: "Enter Cost",
            //   required: true,
            // },
          ]}
          onDataChange={(data) => {
            dispatch(addDynamicFieldSections(data));
            // onSetHandler("billing_details", data);
          }}
        /> */}

        <AddCostInput
          title="Additional Cost"
          minItems={0}
          maxItems={10}
          defaultValue={
            consultationDetail?.proctologyOrNonProctology?.additional_cost?.split(
              ","
            ) || []
          }
          cardClassName="w-full"
          addButtonText="Add Cost"
          inputField={{
            name: "cost",
            label: "Cost",
            type: "number",
            placeholder: "Enter Cost",
            required: true,
          }}
          selectDropDown={{
            name: "service",
            label: "Service",
            required: true,
            placeholder: "Select Service",
            options: serviceCostDropdownData || [],
          }}
        />

        {/* Cost summary  */}
        <CostSummary />

        <View className="mt-6">
          <Input
            type="number"
            id="consultation_discount"
            name="consultation_discount"
            label="Discount in (%)"
            // value={values?.consultation_discount || 0}
            value={discountPercent ? discountPercent : ""}
            onChange={(e) => {
              const discountValue = parseInt(e.target.value, 10);
              if (discountValue > 100) return;
              if (discountValue < 0) return;
              dispatch(discountPercentSlice(e.target.value));
              onSetHandler("consultation_discount", e.target.value);
            }}
            placeholder="Enter Discount"
          />
        </View>
      </View>

      <View>
        <SingleSelector
          id="status"
          label="Consultation Status"
          name="status"
          // error={errorsStatus}
          // value={values?.status || GenericStatus.COMPLETED}
          value={GenericStatus.COMPLETED}
          placeholder="Select Status"
          onChange={(value) => {
            onSetHandler("status", value);
          }}
          options={statusOptions}
        />
      </View>
    </React.Fragment>
  );
};
export default SectionSix;
