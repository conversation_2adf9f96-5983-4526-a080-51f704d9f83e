import * as Yup from "yup";

const today = new Date();
const eighteenYearsAgo = new Date(
  today.getFullYear() - 1,
  today.getMonth(),
  today.getDate()
);

export const validationSchema = Yup.object().shape({
  first_name: Yup.string()
    .matches(
      /^[A-Za-z0-9\s]+$/,
      "First name should contain only letters and numbers"
    )
    .min(2, "First name must be at least 2 characters")
    .max(50, "First name is too long")
    .required("First name is required"),

  last_name: Yup.string()
    .matches(/^[A-Za-z\s]+$/, "Last name should contain only letters")
    // .min(2, "Last name must be at least 2 characters")
    // .max(50, "Last name is too long")
    .required("Last name is required"),

  // email: Yup.string().email("Email is invalid").required("Email is required"),

  phone_no: Yup.string()
    .matches(/^\+?[1-9]\d{9}$/, "Phone number is invalid")
    .required("Phone number is required"),

  // dob: Yup.date()
  //   .max(eighteenYearsAgo, "Patient must be at least 1 year old")
  //   .min(
  //     new Date(today.getFullYear() - 120, today.getMonth(), today.getDate()),
  //     "Date of birth is too far in the past (max 120 years)"
  //   )
  //   .required("Date of birth is required"),
  dob: Yup.date()
    .transform((value, originalValue) => {
      return originalValue === "" ? null : value;
    })
    .max(eighteenYearsAgo, "Patient must be atleast 1 year old")
    .min(
      new Date(today.getFullYear() - 120, today.getMonth(), today.getDate()),
      "Date of birth is too far in the past (max 120 years)"
    )
    .nullable(),
  age: Yup.number()
  .required("Age is required")
  .typeError("Age must be a number")
  .min(1, "Age must be at least 1")
  .max(120, "Age must be less than or equal to 120"),

  gender: Yup.string().required("Gender is required"),
  // marital_status: Yup.string().required("Marital status is required"),
  // id_proof_for_pan: Yup.string().notRequired().matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, "Invalid PAN number"),
  id_proof_for_pan: Yup.string().nullable().test("is-valid-pan", "Invalid PAN number", function (value) {
    if (!value) return true;
    return /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(value);
  }),
  // address: Yup.string()
  //   .min(10, "Address must be at least 10 characters")
  //   .required("Address is required"),

  // city: Yup.string().required("City is required"),

  // state: Yup.string().required("State is required"),

  // country: Yup.string().required("Country is required"),

  // pincode: Yup
  //   .string()
  //   .required('PIN Code is required')
  //   .matches(/^[1-9][0-9]{5}$/, 'PIN Code must be a 6-digit number starting from 1-9'),

  referred_by_name: Yup.string().test(
    "is-valid-name",
    "Referred By should contain only letters",
    (value) => !value || /^[A-Za-z\s]+$/.test(value)
  ),
  referred_by_phone_no: Yup.string().test(
    "is-valid-phone",
    "Phone number is invalid",
    (value) => !value || /^\+?[1-9]\d{9}$/.test(value)
  ),
  referred_by_email: Yup.string().email("Email is invalid").nullable(),
  referred_by_hospital_name: Yup.string().nullable(),
});
