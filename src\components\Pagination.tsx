import React from "react";
import Button from "./button";
import View from "./view";

interface PaginationProps {
  last_page?: number;
  current_page?: number;
  getPageNumberHandler?: (pageNuber: number) => void;
}

const PaginationComponent: React.FC<PaginationProps> = ({
  last_page = 1,
  current_page = 1,
  getPageNumberHandler,
}) => {
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= last_page && getPageNumberHandler) {
      getPageNumberHandler(page);
    }
  };

  return (
    <View>
      {/* <ul>
        {items.map((item, idx) => (
          <li key={idx}>{item.name}</li> // adjust based on your model
        ))}
      </ul> */}

      <View className="flex items-center gap-2 justify-end py-2">
        <Button
          disabled={current_page === 1}
          onPress={() => handlePageChange(current_page - 1)}
        >
          Previous
        </Button>

        {[...Array(last_page)].map((_, index) => {
          const pageNum = index + 1;
          return (
            <Button
              variant="ghost"
              key={pageNum}
              onPress={() => handlePageChange(pageNum)}
              style={{
                fontWeight: pageNum === current_page ? "bold" : "normal",
              }}
              className={pageNum === current_page ? "text-primary" : ""}
            >
              {pageNum}
            </Button>
          );
        })}

        <Button
          className="mr-2"
          disabled={current_page === last_page}
          onPress={() => handlePageChange(current_page + 1)}
        >
          Next
        </Button>
      </View>
    </View>
  );
};

export default PaginationComponent;
