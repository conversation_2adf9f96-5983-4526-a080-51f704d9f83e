import React, { useEffect } from "react";
import View from "@/components/view";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import useForm from "@/utils/custom-hooks/use-form";
import { Consultation } from "@/interfaces/consultation";
import Input from "@/components/input";
import TransferList from "@/components/TransferList";
import { useManagement } from "@/actions/calls/management";

interface SectionEightProps {}

const SectionEight: React.FC<SectionEightProps> = ({}) => {
  const consultationDetail = useSelector(
    (state: RootState) => state.consultation.consultationDetailData
  );
  const consultationDetailData = {
    ...consultationDetail?.consultations,
    ...consultationDetail?.proctologyOrNonProctology,
  };

  const managementDropdownList = useSelector(
    (state: RootState) => state.management.managementDropdownList
  )?.map((item: any) => ({
    id: item?.id,
    label: item?.management_name,
    value: item?.management_name + " ( " + item?.description + " )",
    description: item?.description || "",
  }));

  const { values, handleChange, onSetHandler } = useForm<Consultation | null>(
    consultationDetailData
  );

  const { managementDropdown } = useManagement();

  useEffect(() => {
    managementDropdown((_: boolean) => {});
  }, []);

  return (
    <React.Fragment>
      <View>
        <TransferList
          name="managements"
          label="Managements"
          sourceData={managementDropdownList}
          // sourceData={[
          //   { id: 1, label: "Management 1", value: "management_1" },
          //   { id: 2, label: "Management 2", value: "management_2" },
          //   { id: 3, label: "Management 3", value: "management_3" },
          //   { id: 4, label: "Management 4", value: "management_4" },
          // ]}
          selectedItems={
            values?.managements
              ? Array.isArray(values?.managements)
                ? values?.managements
                : JSON.parse(values?.managements)
              : []
          }
          onSelectionChange={(value) => {
            onSetHandler("managements", value);
          }}
          placeholder="Search managements..."
          sourceTitle="Available Managements"
          selectedTitle="Selected Managements"
          height="150px"
          searchable
          showCount
          allowSelectAll
          // allowCustomValues
        />
      </View>
      <View>
        <Input
          type="date"
          id="managements_date"
          name="managements_date"
          label="Managements Date"
          value={`${values?.managements_date}` || ""}
          onChange={handleChange}
          placeholder="Enter Managements Date"
        />
      </View>
    </React.Fragment>
  );
};
export default SectionEight;
