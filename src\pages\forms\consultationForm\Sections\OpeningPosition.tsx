import { RootState } from "@/actions/store";
import Button from "@/components/button";
import Input from "@/components/input";
import View from "@/components/view";
import { Consultation } from "@/interfaces/consultation";
import useForm from "@/utils/custom-hooks/use-form";
import { BadgePlus, X } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";

type EntryType = {
  secondary_opening_position: string;
  secondary_anal_valve: string;
};

const OpeningPosition: React.FC<{ position: any }> = ({ position }) => {
  const consultationDetailData = useSelector(
    (state: RootState) => state.consultation.consultationDetailData
  );

  const consultationDetail = {
    ...consultationDetailData?.proctologyOrNonProctology,
    ...consultationDetailData?.vitals,
    ...consultationDetailData?.consultations,
  };

  const { values, onSetHandler } = useForm<Consultation | null>(
    consultationDetail
  );

  // const [defaultValue, setDefaultValue] = useState<EntryType[]>([]);
  console.log("values", values);
  

  useEffect(() => {
    if (values?.secondary_opening_position) {
     
      
      const positions = values?.secondary_opening_position?.split("#") || [];
      const valves = values?.secondary_anal_valve?.split("#") || [];
      const entries = positions.map((item: any, index: number) => ({
        secondary_opening_position: item,
        secondary_anal_valve: valves[index] || "",
      }));
      // setDefaultValue(entries);
      setSecondaryOpeningPosition(entries);
    } else {
      setSecondaryOpeningPosition([
        { secondary_opening_position: "", secondary_anal_valve: "" },
      ]);
      // setDefaultValue([{ secondary_opening_position: "", secondary_anal_valve: "" }]);
    }
  }, [values?.secondary_opening_position, values?.secondary_anal_valve]);

  // console.log("defaultValue", defaultValue);
  

  const [secondaryOpeningPosition, setSecondaryOpeningPosition] = useState<
    EntryType[]
  >([]);
  // ([
  //   {
  //     secondary_opening_position:
  //       values?.secondary_opening_position?.split(" ")[0] ?? "",
  //     secondary_anal_valve: values?.secondary_anal_valve?.split(" ")[0] ?? "",
  //   },
  // ]);
  

  const handleInputChange = (
    index: number,
    field: keyof EntryType,
    value: string
  ) => {
    const updated = [...secondaryOpeningPosition];
    updated[index][field] = value;
    setSecondaryOpeningPosition(updated);

    // Optional: If you want to update global state (only when first item changes)
    if (index === 0) {
      onSetHandler(field, value);
    }
  };

  const addEntry = () => {
    setSecondaryOpeningPosition([
      ...secondaryOpeningPosition,
      { secondary_opening_position: "", secondary_anal_valve: "" },
    ]);
  };

  const removeEntry = (index: number) => {
    const updated = secondaryOpeningPosition.filter((_, i) => i !== index);
    setSecondaryOpeningPosition(updated);
  };

  return (
    <>
      {secondaryOpeningPosition.map((entry, index) => (
        <View className="mt-4 border border-border p-2 rounded" key={index}>
          <View className="flex justify-end items-center gap-2">
            {secondaryOpeningPosition.length > 1 && (
              <Button
                type="button"
                onPress={() => removeEntry(index)}
                variant="danger"
              >
                <X size={16} />
              </Button>
            )}
            <Button type="button" onPress={addEntry}>
              <BadgePlus size={16} />
            </Button>
          </View>

          <View className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <View className="flex flex-col gap-4">
              <Input
                // name={`secondary_opening_position_${index}`}
                label="Secondary Opening Position"
                value={entry.secondary_opening_position || ""}
                onChange={(e: any) =>
                  handleInputChange(
                    index,
                    "secondary_opening_position",
                    e.target.value
                  )
                }
                placeholder="Ex: 6"
              />
              <Input value={position} readOnly label="Position suffixed with" />
            </View>

            <View>
              <Input
                // name={`secondary_anal_valve_${index}`}
                label="Secondary Anal Valve"
                value={entry.secondary_anal_valve || ""}
                onChange={(e: any) =>
                  handleInputChange(
                    index,
                    "secondary_anal_valve",
                    e.target.value
                  )
                }
                placeholder="Ex: 6"
              />
            </View>
          </View>
        </View>
      ))}
     <Input hidden name="secondary_opening_position" value={secondaryOpeningPosition.map((entry) => entry.secondary_opening_position).join("#")} />
     <Input hidden name="secondary_anal_valve" value={secondaryOpeningPosition.map((entry) => entry.secondary_anal_valve).join("#")} />
    </>
  );
};

export default OpeningPosition;
