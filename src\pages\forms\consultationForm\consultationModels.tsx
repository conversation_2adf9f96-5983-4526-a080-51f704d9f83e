import React from "react";
import Modal from "@/components/Modal";
import { RootState } from "@/actions/store";
import DiagnosisForm from "../diagnosis/Diagnosis";
import { useDispatch, useSelector } from "react-redux";
import { useDiagnosis } from "@/actions/calls/diagnosis";
import ComorbidityForm from "../comorbidities/Comorbidities";
import { useComorbidity } from "@/actions/calls/comorbidities";
import OnExaminationForms from "../onExamination/OnExamination";
import { useOnExamination } from "@/actions/calls/onExamination";
import { useChiefComplaint } from "@/actions/calls/chiefComplaints";
import { useSurgicalHistory } from "@/actions/calls/surgicalHistory";
import SurgicalHistoryForm from "../surgicalHistory/SurgicalHistory";
import ChiefComplaintForm from "../chief complaints form/chiefComplaintsForm";
import {
  setDiagnosisModel,
  setComorbiditiesModel,
  setOnExaminationModel,
  setChiefComplaintModel,
  setSurgicalHistoryModel,
  setTestModel,
  setDietPlanModel,
} from "@/actions/slices/medicalStatus";
import TestForm from "../test/Test";
import { useTest } from "@/actions/calls/test";
import { useDiet } from "@/actions/calls/diet";
import DietForm from "../diet/Diet";

const ConsultationModels: React.FC<{ children?: React.ReactNode }> = ({
  children,
}) => {
  const dispatch = useDispatch();
  const chiefComplaintModel = useSelector(
    (state: RootState) => state.modelStatus.chiefComplaintStatus
  );
  const surgicalHistoryStatus = useSelector(
    (state: RootState) => state.modelStatus.surgicalHistoryStatus
  );
  const comorbiditiesStatus = useSelector(
    (state: RootState) => state.modelStatus.comorbiditiesStatus
  );
  const onExaminationStatus = useSelector(
    (state: RootState) => state.modelStatus.onExaminationStatus
  );
  const diagnosisStatus = useSelector(
    (state: RootState) => state.modelStatus.diagnosisStatus
  );
  const testStatus = useSelector(
    (state: RootState) => state.modelStatus.testStatus
  );
  const dietPlanStatus = useSelector(
    (state: RootState) => state.modelStatus.dietPlanStatus
  );
  const { dietDropdownHandler } = useDiet();
  const { testDropdownHandler } = useTest();
  const { diagnosisDropdownHandler } = useDiagnosis();
  const { onExaminationDropdownHandler } = useOnExamination();
  const { chiefComplaintDropdownHandler } = useChiefComplaint();
  const { surgicalHistoryDropdownHandler } = useSurgicalHistory();
  const { comorbidityDropdownHandler } = useComorbidity();

  return (
    <React.Fragment>
      {chiefComplaintModel ? (
        <Modal
          size="full"
          isOpen={chiefComplaintModel}
          onClose={() => {
            dispatch(setChiefComplaintModel(false));
          }}
          title="Add Chief Complaint"
        >
          <ChiefComplaintForm
            onModalSuccess={() => {
              dispatch(setChiefComplaintModel(false));
              chiefComplaintDropdownHandler(() => {});
            }}
          />
        </Modal>
      ) : surgicalHistoryStatus ? (
        <Modal
          size="full"
          isOpen={surgicalHistoryStatus}
          onClose={() => {
            dispatch(setSurgicalHistoryModel(false));
          }}
          title="Add Surgical History"
        >
          <SurgicalHistoryForm
            onModalSuccess={() => {
              dispatch(setSurgicalHistoryModel(false));
              surgicalHistoryDropdownHandler(() => {});
            }}
          />
        </Modal>
      ) : comorbiditiesStatus ? (
        <Modal
          size="full"
          isOpen={comorbiditiesStatus}
          onClose={() => {
            dispatch(setComorbiditiesModel(false));
          }}
          title="Add Comorbidity"
        >
          <ComorbidityForm
            onModalSuccess={() => {
              dispatch(setComorbiditiesModel(false));
              comorbidityDropdownHandler(() => {});
            }}
          />
        </Modal>
      ) : onExaminationStatus ? (
        <Modal
          size="full"
          isOpen={onExaminationStatus}
          onClose={() => {
            dispatch(setOnExaminationModel(false));
          }}
          title="Add On Examination"
        >
          <OnExaminationForms
            onModalSuccess={() => {
              dispatch(setOnExaminationModel(false));
              onExaminationDropdownHandler(() => {},"Proctology");
            }}
          />
        </Modal>
      ) : diagnosisStatus ? (
        <Modal
          size="full"
          isOpen={diagnosisStatus}
          onClose={() => {
            dispatch(setDiagnosisModel(false));
          }}
          title="Add Diagnosis"
        >
          <DiagnosisForm
            onModalSuccess={() => {
              dispatch(setDiagnosisModel(false));
              diagnosisDropdownHandler(() => {});
            }}
          />
        </Modal>
      ) : testStatus ? (
        <Modal
          size="full"
          isOpen={testStatus}
          onClose={() => {
            dispatch(setTestModel(false));
          }}
          title="Add Test"
        >
          <TestForm
            onModalSuccess={() => {
              dispatch(setTestModel(false));
              testDropdownHandler(() => {});
            }}
          />
        </Modal>
      ) : dietPlanStatus ? (
        <Modal
          size="full"
          isOpen={dietPlanStatus}
          onClose={() => {
            dispatch(setDietPlanModel(false));
          }}
          title="Add Diet Plan"
        >
          <DietForm
            onModalSuccess={() => {
              dispatch(setDietPlanModel(false));
              dietDropdownHandler(() => {});
            }}
          />
        </Modal>
      ) : (
        children
      )}
    </React.Fragment>
  );
};

export default ConsultationModels;
