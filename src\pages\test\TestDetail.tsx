import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useTest } from "@/actions/calls/test";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { clearTestDetailSlice } from "@/actions/slices/test";
import View from "@/components/view";
import Text from "@/components/text";
import Button from "@/components/button";
import BouncingLoader from "@/components/BouncingLoader";

const TestDetails = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const { testDetailHandler, cleanUp } = useTest();
  const testData = useSelector((state: any) => state.test.testDetailData);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (id) {
      testDetailHandler(id, () => {}, [], (status) => {
        setIsLoading(status === "pending" ? true : status === "failed" ? true : status === "success" && false);
      }
      );
      setLoading(false);
    }
    return () => {
      cleanUp();
      dispatch(clearTestDetailSlice());
    };
  }, [id]);

  if (loading) {
    return (
      <View className="text-center text-muted py-10">Loading test data...</View>
    );
  }

  return (
    <View className="container mx-auto p-4 space-y-6">
      <BouncingLoader isLoading={isLoading} />
      <View className="flex justify-between items-center">
        <View>
          <Text as="h1" className="text-2xl font-bold">
            Test Details
          </Text>
          <Text as="p" className="text-muted-foreground">
            Viewing details for test: {testData?.test_name}
          </Text>
        </View>
        <View className="flex gap-3">
          <Button variant="outline" onClick={() => navigate(-1)}>
            Back to Home
          </Button>
        </View>
      </View>
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-bold">
            {testData?.test_name}
          </CardTitle>
          <CardTitle className="text-sm" style={{ marginTop: "20px" }}>
            Test Price : <span>Rs {testData?.test_price || "N/A"}</span>
          </CardTitle>
          <CardTitle className="text-sm" style={{ marginTop: "20px" }}>
            Tax Price : <span>Rs {testData?.tax_price || "N/A"}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <View
            className="text-muted-foreground text-sm"
            dangerouslySetInnerHTML={{
              __html: testData?.test_description || "",
            }}
          />
        </CardContent>
      </Card>
    </View>
  );
};

export default TestDetails;
