import { useParams } from "react-router-dom";
import { useDoshaApi } from "@/actions/calls/doshaAnalysis/prakriti";

type DoshaAnalysisUrl = "prakriti" | "vikruti" | "agni" | "koshta" | "avastha";

const useDoshaAnalysis = () => {
  const { type } = useParams();
  const chooseFunction: Record<DoshaAnalysisUrl, string> = {
    prakriti: "prakriti",
    vikruti: "vikruti",
    agni: "agni",
    koshta: "koshta",
    avastha: "avastha",
  };
  const {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    add<PERSON><PERSON><PERSON>,
    edit<PERSON><PERSON><PERSON>,
    delete<PERSON><PERSON><PERSON>,
    OptionsList<PERSON>andler,
    cleanUp,
  } = useDoshaApi(chooseFunction[type as DoshaAnalysisUrl]);

  return {
    cleanUp,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    addH<PERSON><PERSON>,
    edit<PERSON><PERSON><PERSON>,
    delete<PERSON><PERSON><PERSON>,
    OptionsListHandler,
  };
};

export default useDoshaAnalysis;
