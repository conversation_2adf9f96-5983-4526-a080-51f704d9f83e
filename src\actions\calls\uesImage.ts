import LaunchApi from "@/actions/api";
import { ApiCallback } from "@/interfaces/api";
import { IMAGES_URL } from "@/utils/urls/backend";
import { AuthPayload } from "@/interfaces/slices/auth";

const api = new LaunchApi();
export const imageUpload = async (
  data: any,
  callback: ApiCallback
): Promise<void> => {
  try {
    await api.multiformData(
      IMAGES_URL,
      data,
      (_: AuthPayload, success: boolean, statusCode: number) => {
        if (success && statusCode === 200) {
          return callback(true, { success: true });
        } else if (statusCode && statusCode !== 204) {
          return callback(false, { success: false });
        }
      }
    );
  } catch (error) {
    console.error(error);
    callback(false, { success: false });
  }
};
