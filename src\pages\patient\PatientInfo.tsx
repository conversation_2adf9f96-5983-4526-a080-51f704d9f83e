import Text from "@/components/text";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import View from "@/components/view";
import { DATE_FORMAT } from "@/utils/urls/frontend";
import dayjs from "dayjs";
import { Calendar, Mail, MapPin, Mars, Phone, Venus, VenusAndMars, Activity, Shield, AlertCircle, User } from "lucide-react";

const PatientInfo: React.FC<{
    patient: any
}> = ({patient}) => {
  return (
    <Card className="mb-6 overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10 pb-6">
        <View className="flex items-start justify-between">
          <View className="flex items-center">
            <View className="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center text-primary text-xl font-bold mr-4 border-2 border-primary/20">
              {patient?.first_name?.charAt(0)}
              {patient?.last_name?.charAt(0)}
            </View>
            <View>
              <CardTitle className="text-2xl text-primary mb-1">
                {patient?.first_name} {patient?.last_name}
              </CardTitle>
              <CardDescription className="flex items-center gap-4 text-base">
                <Text as="span" className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  ID: {patient?.patient_number || "N/A"}
                </Text>
                <Text as="span" className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  {patient?.dob?dayjs(patient?.dob).format(DATE_FORMAT):""} ({patient?.age} years)
                </Text>
              </CardDescription>
            </View>
          </View>
          <View className="bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full border border-primary/20">
            {patient?.gender ? (
              patient?.gender === "male" ? (
                <Text as="span" className="flex items-center gap-2 text-primary font-medium">
                  <Mars className="size-4" /> 
                  {patient?.gender.charAt(0).toUpperCase() + patient?.gender.slice(1)}
                </Text>
              ) : patient?.gender === "female" ? (
                <Text as="span" className="flex items-center gap-2 text-primary font-medium">
                  <Venus className="size-4" /> 
                  {patient?.gender.charAt(0).toUpperCase() + patient?.gender.slice(1)}
                </Text>
              ) : (
                <Text as="span" className="flex items-center gap-2 text-primary font-medium">
                  <VenusAndMars className="size-4" /> 
                  {patient?.gender}
                </Text>
              )
            ) : (
              <Text as="span" className="flex items-center gap-2 text-muted-foreground">
                <VenusAndMars className="size-4" /> 
                Not specified
              </Text>
            )}
          </View>
        </View>
      </CardHeader>
      
      <CardContent className="pt-6">
        {/* Contact Information */}
        <View className="mb-6">
          <Text as="h4" className="text-sm font-semibold text-muted-foreground uppercase tracking-wide mb-3">
            Contact Information
          </Text>
          <View className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <View className="flex items-center p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
              <Phone className="h-5 w-5 text-primary mr-3 flex-shrink-0" />
              <View>
                <Text as="p" className="text-sm text-muted-foreground">Phone</Text>
                <Text as="p" className="font-medium">
                  <a href={`tel:${patient?.phone_no}`} className="hover:text-primary transition-colors">
                    {patient?.phone_no || "N/A"}
                  </a>
                </Text>
              </View>
            </View>
            
            <View className="flex items-center p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
              <Mail className="h-5 w-5 text-primary mr-3 flex-shrink-0" />
              <View>
                <Text as="p" className="text-sm text-muted-foreground">Email</Text>
                <Text as="p" className="font-medium">
                  <a href={`mailto:${patient?.email}`} className="hover:text-primary transition-colors">
                    {patient?.email || "N/A"}
                  </a>
                </Text>
              </View>
            </View>
            
            <View className="flex items-start p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors lg:col-span-1">
              <MapPin className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
              <View>
                <Text as="p" className="text-sm text-muted-foreground">Address</Text>
                <Text as="p" className="font-medium text-sm leading-relaxed">
                  {patient?.address ? (
                    `${patient.address}, ${patient?.city || ''}, ${patient?.state || ''} ${patient?.zip_code || ''}`
                  ) : "N/A"}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Medical Information */}
        <View>
          <Text as="h4" className="text-sm font-semibold text-muted-foreground uppercase tracking-wide mb-3">
            Medical Information
          </Text>
          <View className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <View className="flex items-center p-4 rounded-lg border border-red-200 bg-red-50 transition-colors">
              <Activity className="h-6 w-6 text-red-600 mr-3" />
              <View>
                <Text as="p" className="text-sm text-red-600/80 font-medium">Blood Type</Text>
                <Text as="p" className="text-lg font-bold text-red-700">
                  {patient?.blood_group || "N/A"}
                </Text>
              </View>
            </View>
            
            <View className="flex items-center p-4 rounded-lg border border-blue-200 bg-blue-50 transition-colors">
              <Shield className="h-6 w-6 text-blue-600 mr-3" />
              <View>
                <Text as="p" className="text-sm text-blue-600/80 font-medium">Insurance</Text>
                <Text as="p" className="text-lg font-bold text-blue-700">
                  {patient?.insurance_provider || "N/A"}
                </Text>
              </View>
            </View>
            
            <View className="flex items-center p-4 rounded-lg border border-amber-200 bg-amber-50 transition-colors">
              <AlertCircle className="h-6 w-6 text-amber-600 mr-3" />
              <View>
                <Text as="p" className="text-sm text-amber-600/80 font-medium">Emergency Contact</Text>
                <Text as="p" className="text-lg font-bold text-amber-700">
                  {patient?.attendant_with_patient_phone_no || "N/A"}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </CardContent>
    </Card>
  );
};

export default PatientInfo;