import React, { useState, useCallback } from "react";
import View from "./view";
import Text from "./text";
import { ArchiveX, Check, X, Plus } from "lucide-react";
import BouncingLoader from "./BouncingLoader";
import Input from "./input";
import Button from "./button";
import {
  Table,
  TableRow,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from "./ui/table";

interface EditableTableProps {
  tableData: any[][];
  tableHeaders: string[];
  isLoading?: boolean;
  emptyMessage?: string;
  emptyIcon?: React.ReactNode;
  getRowKey?: (row: any[], rowIndex: number) => string | number;
  onRowClick?: (row: any[], rowIndex: number) => void;
  onCellEdit?: (rowIndex: number, colIndex: number, value: any) => void;
  onRowAdd?: (newRow: any[]) => void;
  onRowDelete?: (rowIndex: number) => void;
  editable?: boolean;
  addRowEnabled?: boolean;
  deleteRowEnabled?: boolean;
  header?: {
    search?: React.ReactNode;
    sort?: React.ReactNode;
    filter?: React.ReactNode;
    action?: React.ReactNode;
  };
  footer?: {
    pagination?: React.ReactNode;
  };
  onSubmitCompleteRow?: (row: any[]) => void;
}

const EditableTable: React.FC<EditableTableProps> = ({
  tableData,
  tableHeaders,
  isLoading = false,
  emptyMessage = "No Data Found!",
  emptyIcon = (
    <ArchiveX className="w-10 h-10 mx-auto mb-2 bg-primary/10 p-2 rounded-full text-primary" />
  ),
  getRowKey,
  //   onRowClick,
  onCellEdit,
  onRowAdd,
  onRowDelete,
  editable = true,
  addRowEnabled = true,
  deleteRowEnabled = false,
  header,
  footer,
  onSubmitCompleteRow,
}) => {
  const [editingCell, setEditingCell] = useState<{
    row: number;
    col: number;
  } | null>(null);

  const [editValue, setEditValue] = useState<string>("");
  const [newRow, setNewRow] = useState<string[]>([]);
  const [isAddingRow, setIsAddingRow] = useState(false);

  const startEdit = useCallback(
    (rowIndex: number, colIndex: number, currentValue: any) => {
      setEditingCell({ row: rowIndex, col: colIndex });
      setEditValue(String(currentValue || ""));
    },
    []
  );

  const saveEdit = useCallback(() => {
    if (editingCell && onCellEdit) {
      onCellEdit(editingCell.row, editingCell.col + 1, editValue);
    }
    setEditingCell(null);
    setEditValue("");
  }, [editingCell, editValue, onCellEdit]);

  const cancelEdit = useCallback(() => {
    setEditingCell(null);
    setEditValue("");
  }, []);

  const startAddRow = useCallback(() => {
    setNewRow(new Array(tableHeaders.length).fill(""));
    setIsAddingRow(true);
  }, [tableHeaders.length, addRowEnabled]);

  const saveNewRow = useCallback(() => {
    if (onRowAdd && newRow.some((cell) => cell.trim() !== "")) {
      onRowAdd(newRow);
    }
    setNewRow([]);
    setIsAddingRow(false);
  }, [newRow, onRowAdd]);

  const cancelAddRow = useCallback(() => {
    setNewRow([]);
    setIsAddingRow(false);
  }, []);

  const updateNewRowCell = useCallback((colIndex: number, value: string) => {
    setNewRow((prev) => {
      const updated = [...prev];
      updated[colIndex] = value;
      return updated;
    });
  }, []);

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter") {
        saveEdit();
      } else if (e.key === "Escape") {
        cancelEdit();
      }
    },
    [saveEdit, cancelEdit]
  );

  const handleNewRowKeyPress = useCallback(
    (e: React.KeyboardEvent, colIndex: number) => {
      if (e.key === "Enter") {
        if (colIndex === tableHeaders.length - 1) {
          saveNewRow();
        } else {
          // Focus next input
          const nextInput = e.currentTarget
            .closest("tr")
            ?.querySelector(
              `input[data-col="${colIndex + 1}"]`
            ) as HTMLInputElement;
          nextInput?.focus();
        }
      } else if (e.key === "Escape") {
        cancelAddRow();
      }
    },
    [tableHeaders.length, saveNewRow, cancelAddRow]
  );

  return (
    <View>
      {/* Header controls */}
      {addRowEnabled && (
        <View>
          <Button
            variant="primary"
            onClick={startAddRow}
            disabled={isAddingRow}
            className="flex items-center justify-center gap-2 w-full"
          >
            <Plus className="w-4 h-4" />
            Add Row
          </Button>
        </View>
      )}
      <View className="p-4 border-b border-border bg-card flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <View className="flex gap-2 w-full justify-between items-center">
          {header?.search}
          {/* <View className="flex gap-3"> */}
          <View>
            {header?.sort}
            {header?.filter}
            {header?.action && (
              <View className="shrink-0">{header.action}</View>
            )}
          </View>
        </View>
      </View>
      {/* Table */}
      <View className="overflow-x-auto">
        <Table className="w-full min-w-max">
          <TableHeader>
            <TableRow className="bg-muted/50 font-bold border-b border-border">
              {tableHeaders
                .filter((x) => x !== "")
                .map((header, index) => (
                  <TableHead
                    key={index}
                    className="py-3 px-4 text-sm font-bold text-muted-foreground"
                  >
                    {header}
                  </TableHead>
                ))}
              {(editable || deleteRowEnabled) && (
                <TableHead className="py-3 px-4 text-sm font-bold text-muted-foreground w-20">
                  Actions
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={
                    tableHeaders.length + (editable || deleteRowEnabled ? 1 : 0)
                  }
                  className="py-4 text-center relative"
                >
                  <View className="w-full z-50">
                    <BouncingLoader notFixed isLoading={isLoading} />
                  </View>
                </TableCell>
              </TableRow>
            ) : !tableData || (tableData.length === 0 && !isAddingRow) ? (
              <TableRow>
                <TableCell
                  colSpan={
                    tableHeaders.length + (editable || deleteRowEnabled ? 1 : 0)
                  }
                  className="py-6 px-4 text-center text-muted-foreground"
                >
                  {emptyIcon}
                  <Text as="span">{emptyMessage}</Text>
                </TableCell>
              </TableRow>
            ) : (
              <>
                {tableData.map((row, rowIndex, completeCell) => (
                  <TableRow
                    key={getRowKey ? getRowKey(row, rowIndex) : rowIndex}
                    className="border-b border-border hover:bg-muted/50 transition-colors"
                  >
                    {row
                      .filter((_, colIndex) => colIndex !== 0)
                      .map((cell, colIndex) => (
                        <TableCell
                          key={`td_${rowIndex}_${colIndex}`}
                          className="py-3 px-4 whitespace-nowrap"
                        >
                          {editingCell?.row === rowIndex &&
                          editingCell?.col === colIndex ? (
                            <View className="flex items-center gap-2">
                              <Input
                                value={editValue}
                                onChange={(
                                  e: React.ChangeEvent<HTMLInputElement>
                                ) => setEditValue(e.target.value)}
                                onKeyDown={handleKeyPress}
                                className="min-w-0 h-8"
                                autoFocus
                              />
                              <Button
                                variant="ghost"
                                //   size="sm"
                                onClick={saveEdit}
                                className="h-8 w-8 p-0"
                              >
                                <Check className="w-4 h-4 text-green-600" />
                              </Button>
                              <Button
                                variant="ghost"
                                //   size="sm"
                                onClick={cancelEdit}
                                className="h-8 w-8 p-0"
                              >
                                <X className="w-4 h-4 text-red-600" />
                              </Button>
                            </View>
                          ) : (
                            <View
                              className={`${
                                editable
                                  ? "cursor-pointer hover:bg-muted/30 rounded px-2 py-1 -mx-2 -my-1"
                                  : ""
                              }`}
                              onClick={() =>
                                editable && startEdit(rowIndex, colIndex, cell)
                              }
                            >
                              {cell || (
                                <span className="text-muted-foreground italic">
                                  Click to edit
                                </span>
                              )}
                            </View>
                          )}
                        </TableCell>
                      ))}
                    {(editable || deleteRowEnabled) && (
                      <TableCell className="">
                        <View className="flex items-center gap-1">
                          {deleteRowEnabled && onRowDelete && (
                            <Button
                              variant="ghost"
                              //   size="sm"
                              onClick={() => onRowDelete(rowIndex)}
                              className="h-8 w-8"
                            >
                              <X className="w-4 h-4 text-red-600 hover:text-red-700" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            // onPress={() => {
                            //   row.map((cell, colIndex) => {
                            //     console.log(
                            //       cell,
                            //       completeCell[rowIndex][colIndex],
                            //       tableHeaders,
                            //       tableData
                            //     );
                            //   });
                            //   // console.log(completeCell);
                            // }}
                            onPress={() => {
                              onSubmitCompleteRow?.(completeCell[rowIndex]);
                            }}
                          >
                            <Check className="w-4 h-4 text-green-600" />
                          </Button>
                        </View>
                      </TableCell>
                    )}
                  </TableRow>
                ))}

                {/* Add new row */}
                {isAddingRow && (
                  <TableRow className="border-b border-border bg-muted/20">
                    {tableHeaders.map((_, colIndex) => (
                      <TableCell key={`new_${colIndex}`} className="py-3 px-4">
                        <Input
                          data-col={colIndex}
                          value={newRow[colIndex] || ""}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            updateNewRowCell(colIndex, e.target.value)
                          }
                          onKeyDown={(e: React.KeyboardEvent) =>
                            handleNewRowKeyPress(e, colIndex)
                          }
                          placeholder={`Enter ${tableHeaders[colIndex]}`}
                          className="h-8"
                        />
                      </TableCell>
                    ))}
                    {(editable || deleteRowEnabled) && (
                      <TableCell className="py-3 px-4">
                        <View className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            // size="sm"
                            onClick={saveNewRow}
                            className="h-8 w-8 p-0"
                          >
                            <Check className="w-4 h-4 text-green-600" />
                          </Button>
                          <Button
                            variant="ghost"
                            // size="sm"
                            onClick={cancelAddRow}
                            className="h-8 w-8 p-0"
                          >
                            <X className="w-4 h-4 text-red-600" />
                          </Button>
                        </View>
                      </TableCell>
                    )}
                  </TableRow>
                )}
              </>
            )}
          </TableBody>
        </Table>
      </View>

      {/* Footer (pagination) */}
      {footer?.pagination && <View className="pt-4">{footer.pagination}</View>}
    </View>
  );
};

export default EditableTable;
