/**
 * Enhanced EditableTable Component
 *
 * This component provides a flexible, editable table that supports multiple input types
 * while maintaining full backward compatibility with existing implementations.
 *
 * Features:
 * - Multiple input types: text, email, password, number, date, datetime-local, select, checkbox, textarea, custom
 * - Validation support with custom validation functions
 * - Keyboard navigation (Enter to save/move to next field, Escape to cancel, Ctrl+Enter for textarea)
 * - Type-safe configuration with TypeScript
 * - Backward compatibility - existing code works without changes
 *
 * Usage:
 *
 * Basic usage (backward compatible):
 * <EditableTable
 *   tableData={data}
 *   tableHeaders={headers}
 *   onCellEdit={handleEdit}
 * />
 *
 * Enhanced usage with column configurations:
 * <EditableTable
 *   tableData={data}
 *   tableHeaders={headers}
 *   columnConfigs={[
 *     { header: 'Name', type: 'text', required: true },
 *     { header: 'Email', type: 'email', validation: emailValidator },
 *     { header: 'Active', type: 'checkbox' },
 *     { header: 'Role', type: 'select', options: roleOptions },
 *   ]}
 *   onCellEdit={handleEdit}
 * />
 */

import React, { useState, useCallback } from "react";
import View from "./view";
import Text from "./text";
import { ArchiveX, Check, X, Plus } from "lucide-react";
import BouncingLoader from "./BouncingLoader";
import Input from "./input";
import Button from "./button";
import Select from "./Select";
import Checkbox from "./CheckBox";
import Textarea from "./Textarea";
import SingleSelector from "./SingleSelector";
import {
  Table,
  TableRow,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from "./ui/table";

// Column configuration types
export type ColumnType = 'text' | 'select' | 'checkbox' | 'textarea' | 'date' | 'datetime-local' | 'number' | 'email' | 'password' | 'custom';

export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

export interface ColumnConfig {
  header: string;
  type: ColumnType;
  key?: string; // Optional key for data mapping
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: SelectOption[]; // For select type
  validation?: (value: any) => string | null; // Custom validation function
  customComponent?: React.ComponentType<{
    value: any;
    onChange: (value: any) => void;
    onKeyDown?: (e: React.KeyboardEvent) => void;
    className?: string;
    placeholder?: string;
    disabled?: boolean;
    autoFocus?: boolean;
  }>; // For custom type
  props?: Record<string, any>; // Additional props for the input component
}

interface EditableTableProps {
  tableData: any[][];
  tableHeaders: string[];
  columnConfigs?: ColumnConfig[]; // New optional prop for enhanced functionality
  isLoading?: boolean;
  emptyMessage?: string;
  emptyIcon?: React.ReactNode;
  getRowKey?: (row: any[], rowIndex: number) => string | number;
  onRowClick?: (row: any[], rowIndex: number) => void;
  onCellEdit?: (rowIndex: number, colIndex: number, value: any) => void;
  onRowAdd?: (newRow: any[]) => void;
  onRowDelete?: (rowIndex: number) => void;
  editable?: boolean;
  addRowEnabled?: boolean;
  deleteRowEnabled?: boolean;
  header?: {
    search?: React.ReactNode;
    sort?: React.ReactNode;
    filter?: React.ReactNode;
    action?: React.ReactNode;
  };
  footer?: {
    pagination?: React.ReactNode;
  };
  onSubmitCompleteRow?: (row: any[]) => void;
}

// Helper function to get column configuration
const getColumnConfig = (columnConfigs: ColumnConfig[] | undefined, tableHeaders: string[], colIndex: number): ColumnConfig => {
  if (columnConfigs && columnConfigs[colIndex]) {
    return columnConfigs[colIndex];
  }

  // Default configuration for backward compatibility
  return {
    header: tableHeaders[colIndex] || '',
    type: 'text',
    placeholder: `Enter ${tableHeaders[colIndex] || 'value'}`,
  };
};

// Helper function to validate cell value
const validateCellValue = (value: any, config: ColumnConfig): string | null => {
  // Required field validation
  if (config.required) {
    if (config.type === 'checkbox') {
      // For checkboxes, we might want to require them to be checked
      // This is optional based on use case
    } else if (!value || (typeof value === 'string' && value.trim() === '')) {
      return `${config.header} is required`;
    }
  }

  // Type-specific validation
  if (value && value !== '') {
    switch (config.type) {
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          return `${config.header} must be a valid email address`;
        }
        break;
      case 'number':
        if (isNaN(Number(value))) {
          return `${config.header} must be a valid number`;
        }
        break;
    }
  }

  // Custom validation
  if (config.validation) {
    return config.validation(value);
  }

  return null;
};

// Dynamic input renderer
const renderInputComponent = (
  config: ColumnConfig,
  value: any,
  onChange: (value: any) => void,
  onKeyDown?: (e: React.KeyboardEvent) => void,
  className?: string,
  autoFocus?: boolean,
  disabled?: boolean
): React.ReactNode => {
  const commonProps = {
    value,
    onChange,
    onKeyDown,
    className: className || "min-w-0 h-8",
    placeholder: config.placeholder,
    disabled: disabled || config.disabled,
    autoFocus,
    ...config.props,
  };

  switch (config.type) {
    case 'text':
    case 'email':
    case 'password':
    case 'number':
      return (
        <Input
          type={config.type}
          {...commonProps}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
        />
      );

    case 'date':
    case 'datetime-local':
      return (
        <Input
          type={config.type}
          {...commonProps}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
        />
      );

    case 'select':
      return (
        <Select
          {...commonProps}
          options={config.options || []}
          onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onChange(e.target.value)}
        />
      );

    case 'checkbox':
      return (
        <Checkbox
          checked={Boolean(value)}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.checked)}
          className={className}
          disabled={disabled || config.disabled}
        />
      );

    case 'textarea':
      return (
        <Textarea
          {...commonProps}
          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => onChange(e.target.value)}
        />
      );

    case 'custom':
      if (config.customComponent) {
        const CustomComponent = config.customComponent;
        return (
          <CustomComponent
            value={value}
            onChange={onChange}
            onKeyDown={onKeyDown}
            className={className}
            placeholder={config.placeholder}
            disabled={disabled || config.disabled}
            autoFocus={autoFocus}
          />
        );
      }
      // Fallback to text input if custom component is not provided
      return (
        <Input
          type="text"
          {...commonProps}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
        />
      );

    default:
      return (
        <Input
          type="text"
          {...commonProps}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
        />
      );
  }
};

const EditableTable: React.FC<EditableTableProps> = ({
  tableData,
  tableHeaders,
  columnConfigs,
  isLoading = false,
  emptyMessage = "No Data Found!",
  emptyIcon = (
    <ArchiveX className="w-10 h-10 mx-auto mb-2 bg-primary/10 p-2 rounded-full text-primary" />
  ),
  getRowKey,
  //   onRowClick,
  onCellEdit,
  onRowAdd,
  onRowDelete,
  editable = true,
  addRowEnabled = true,
  deleteRowEnabled = false,
  header,
  footer,
  onSubmitCompleteRow,
}) => {
  const [editingCell, setEditingCell] = useState<{
    row: number;
    col: number;
  } | null>(null);

  const [editValue, setEditValue] = useState<any>("");
  const [newRow, setNewRow] = useState<any[]>([]);
  const [isAddingRow, setIsAddingRow] = useState(false);

  const startEdit = useCallback(
    (rowIndex: number, colIndex: number, currentValue: any) => {
      setEditingCell({ row: rowIndex, col: colIndex });
      const config = getColumnConfig(columnConfigs, tableHeaders, colIndex);

      // Handle different data types for initial value
      if (config.type === 'checkbox') {
        setEditValue(Boolean(currentValue));
      } else if (config.type === 'number') {
        setEditValue(currentValue ? Number(currentValue) : '');
      } else {
        setEditValue(currentValue || "");
      }
    },
    [columnConfigs, tableHeaders]
  );

  const saveEdit = useCallback(() => {
    if (editingCell && onCellEdit) {
      const config = getColumnConfig(columnConfigs, tableHeaders, editingCell.col);

      // Validate the value
      const validationError = validateCellValue(editValue, config);
      if (validationError) {
        // You could show an error message here or integrate with a toast system
        console.warn('Validation error:', validationError);
        // For now, we'll still allow the save but log the error
        // In a real implementation, you might want to prevent saving or show user feedback
      }

      onCellEdit(editingCell.row, editingCell.col + 1, editValue);
    }
    setEditingCell(null);
    setEditValue("");
  }, [editingCell, editValue, onCellEdit, columnConfigs, tableHeaders]);

  const cancelEdit = useCallback(() => {
    setEditingCell(null);
    setEditValue("");
  }, []);

  const startAddRow = useCallback(() => {
    // Initialize new row with appropriate default values based on column types
    const initialRow = tableHeaders.map((_, colIndex) => {
      const config = getColumnConfig(columnConfigs, tableHeaders, colIndex);
      switch (config.type) {
        case 'checkbox':
          return false;
        case 'number':
          return '';
        default:
          return '';
      }
    });
    setNewRow(initialRow);
    setIsAddingRow(true);
  }, [tableHeaders, columnConfigs, addRowEnabled]);

  const saveNewRow = useCallback(() => {
    if (onRowAdd) {
      // Check if any required fields are filled or if any non-empty values exist
      const hasValidData = newRow.some((cell, colIndex) => {
        const config = getColumnConfig(columnConfigs, tableHeaders, colIndex);
        if (config.type === 'checkbox') {
          return true; // Checkboxes are always valid
        }
        return cell && String(cell).trim() !== "";
      });

      if (hasValidData) {
        onRowAdd(newRow);
      }
    }
    setNewRow([]);
    setIsAddingRow(false);
  }, [newRow, onRowAdd, columnConfigs, tableHeaders]);

  const cancelAddRow = useCallback(() => {
    setNewRow([]);
    setIsAddingRow(false);
  }, []);

  const updateNewRowCell = useCallback((colIndex: number, value: any) => {
    setNewRow((prev) => {
      const updated = [...prev];
      updated[colIndex] = value;
      return updated;
    });
  }, []);

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      const config = editingCell ? getColumnConfig(columnConfigs, tableHeaders, editingCell.col) : null;

      if (e.key === "Enter") {
        // For textarea, allow Enter to create new lines unless Ctrl+Enter is pressed
        if (config?.type === 'textarea' && !e.ctrlKey) {
          return;
        }
        saveEdit();
      } else if (e.key === "Escape") {
        cancelEdit();
      }
    },
    [saveEdit, cancelEdit, editingCell, columnConfigs, tableHeaders]
  );

  const handleNewRowKeyPress = useCallback(
    (e: React.KeyboardEvent, colIndex: number) => {
      const config = getColumnConfig(columnConfigs, tableHeaders, colIndex);

      if (e.key === "Enter") {
        // For textarea, allow Enter to create new lines unless Ctrl+Enter is pressed
        if (config.type === 'textarea' && !e.ctrlKey) {
          return;
        }

        if (colIndex === tableHeaders.length - 1) {
          saveNewRow();
        } else {
          // Focus next input - try different selectors for different input types
          const currentRow = e.currentTarget.closest("tr");
          const nextInput = currentRow?.querySelector(
            `[data-col="${colIndex + 1}"]`
          ) as HTMLElement;

          if (nextInput) {
            // For different input types, focus appropriately
            if (nextInput.tagName === 'INPUT' || nextInput.tagName === 'SELECT' || nextInput.tagName === 'TEXTAREA') {
              nextInput.focus();
            } else {
              // For custom components, try to find focusable element inside
              const focusableElement = nextInput.querySelector('input, select, textarea, button') as HTMLElement;
              focusableElement?.focus();
            }
          }
        }
      } else if (e.key === "Escape") {
        cancelAddRow();
      }
    },
    [tableHeaders.length, saveNewRow, cancelAddRow, columnConfigs]
  );

  return (
    <View>
      {/* Header controls */}
      {addRowEnabled && (
        <View>
          <Button
            variant="primary"
            onClick={startAddRow}
            disabled={isAddingRow}
            className="flex items-center justify-center gap-2 w-full"
          >
            <Plus className="w-4 h-4" />
            Add Row
          </Button>
        </View>
      )}
      <View className="p-4 border-b border-border bg-card flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <View className="flex gap-2 w-full justify-between items-center">
          {header?.search}
          {/* <View className="flex gap-3"> */}
          <View>
            {header?.sort}
            {header?.filter}
            {header?.action && (
              <View className="shrink-0">{header.action}</View>
            )}
          </View>
        </View>
      </View>
      {/* Table */}
      <View className="overflow-x-auto">
        <Table className="w-full min-w-max">
          <TableHeader>
            <TableRow className="bg-muted/50 font-bold border-b border-border">
              {tableHeaders
                .filter((x) => x !== "")
                .map((header, index) => (
                  <TableHead
                    key={index}
                    className="py-3 px-4 text-sm font-bold text-muted-foreground"
                  >
                    {header}
                  </TableHead>
                ))}
              {(editable || deleteRowEnabled) && (
                <TableHead className="py-3 px-4 text-sm font-bold text-muted-foreground w-20">
                  Actions
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={
                    tableHeaders.length + (editable || deleteRowEnabled ? 1 : 0)
                  }
                  className="py-4 text-center relative"
                >
                  <View className="w-full z-50">
                    <BouncingLoader notFixed isLoading={isLoading} />
                  </View>
                </TableCell>
              </TableRow>
            ) : !tableData || (tableData.length === 0 && !isAddingRow) ? (
              <TableRow>
                <TableCell
                  colSpan={
                    tableHeaders.length + (editable || deleteRowEnabled ? 1 : 0)
                  }
                  className="py-6 px-4 text-center text-muted-foreground"
                >
                  {emptyIcon}
                  <Text as="span">{emptyMessage}</Text>
                </TableCell>
              </TableRow>
            ) : (
              <>
                {tableData.map((row, rowIndex, completeCell) => (
                  <TableRow
                    key={getRowKey ? getRowKey(row, rowIndex) : rowIndex}
                    className="border-b border-border hover:bg-muted/50 transition-colors"
                  >
                    {row
                      .filter((_, colIndex) => colIndex !== 0)
                      .map((cell, colIndex) => (
                        <TableCell
                          key={`td_${rowIndex}_${colIndex}`}
                          className="py-3 px-4 whitespace-nowrap"
                        >
                          {editingCell?.row === rowIndex &&
                          editingCell?.col === colIndex ? (
                            <View className="flex items-center gap-2">
                              {renderInputComponent(
                                getColumnConfig(columnConfigs, tableHeaders, colIndex),
                                editValue,
                                setEditValue,
                                handleKeyPress,
                                "min-w-0 h-8",
                                true
                              )}
                              <Button
                                variant="ghost"
                                //   size="sm"
                                onClick={saveEdit}
                                className="h-8 w-8 p-0"
                              >
                                <Check className="w-4 h-4 text-green-600" />
                              </Button>
                              <Button
                                variant="ghost"
                                //   size="sm"
                                onClick={cancelEdit}
                                className="h-8 w-8 p-0"
                              >
                                <X className="w-4 h-4 text-red-600" />
                              </Button>
                            </View>
                          ) : (
                            <View
                              className={`${
                                editable
                                  ? "cursor-pointer hover:bg-muted/30 rounded px-2 py-1 -mx-2 -my-1"
                                  : ""
                              }`}
                              onClick={() =>
                                editable && startEdit(rowIndex, colIndex, cell)
                              }
                            >
                              {(() => {
                                const config = getColumnConfig(columnConfigs, tableHeaders, colIndex);

                                // Handle different display formats based on column type
                                if (config.type === 'checkbox') {
                                  return (
                                    <Checkbox
                                      checked={Boolean(cell)}
                                      onChange={() => {}} // Read-only display
                                      disabled={true}
                                    />
                                  );
                                } else if (config.type === 'select' && config.options) {
                                  const option = config.options.find(opt => opt.value === cell);
                                  return option ? option.label : cell;
                                } else if (cell !== null && cell !== undefined && cell !== '') {
                                  return String(cell);
                                } else {
                                  return (
                                    <span className="text-muted-foreground italic">
                                      Click to edit
                                    </span>
                                  );
                                }
                              })()}
                            </View>
                          )}
                        </TableCell>
                      ))}
                    {(editable || deleteRowEnabled) && (
                      <TableCell className="">
                        <View className="flex items-center gap-1">
                          {deleteRowEnabled && onRowDelete && (
                            <Button
                              variant="ghost"
                              //   size="sm"
                              onClick={() => onRowDelete(rowIndex)}
                              className="h-8 w-8"
                            >
                              <X className="w-4 h-4 text-red-600 hover:text-red-700" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            // onPress={() => {
                            //   row.map((cell, colIndex) => {
                            //     console.log(
                            //       cell,
                            //       completeCell[rowIndex][colIndex],
                            //       tableHeaders,
                            //       tableData
                            //     );
                            //   });
                            //   // console.log(completeCell);
                            // }}
                            onPress={() => {
                              onSubmitCompleteRow?.(completeCell[rowIndex]);
                            }}
                          >
                            <Check className="w-4 h-4 text-green-600" />
                          </Button>
                        </View>
                      </TableCell>
                    )}
                  </TableRow>
                ))}

                {/* Add new row */}
                {isAddingRow && (
                  <TableRow className="border-b border-border bg-muted/20">
                    {tableHeaders.map((_, colIndex) => (
                      <TableCell key={`new_${colIndex}`} className="py-3 px-4">
                        <View data-col={colIndex}>
                          {renderInputComponent(
                            getColumnConfig(columnConfigs, tableHeaders, colIndex),
                            newRow[colIndex] || (getColumnConfig(columnConfigs, tableHeaders, colIndex).type === 'checkbox' ? false : ''),
                            (value) => updateNewRowCell(colIndex, value),
                            (e: React.KeyboardEvent) => handleNewRowKeyPress(e, colIndex),
                            "h-8"
                          )}
                        </View>
                      </TableCell>
                    ))}
                    {(editable || deleteRowEnabled) && (
                      <TableCell className="py-3 px-4">
                        <View className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            // size="sm"
                            onClick={saveNewRow}
                            className="h-8 w-8 p-0"
                          >
                            <Check className="w-4 h-4 text-green-600" />
                          </Button>
                          <Button
                            variant="ghost"
                            // size="sm"
                            onClick={cancelAddRow}
                            className="h-8 w-8 p-0"
                          >
                            <X className="w-4 h-4 text-red-600" />
                          </Button>
                        </View>
                      </TableCell>
                    )}
                  </TableRow>
                )}
              </>
            )}
          </TableBody>
        </Table>
      </View>

      {/* Footer (pagination) */}
      {footer?.pagination && <View className="pt-4">{footer.pagination}</View>}
    </View>
  );
};

export default EditableTable;
