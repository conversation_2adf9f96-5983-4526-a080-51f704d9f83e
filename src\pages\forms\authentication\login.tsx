import React, { useState } from "react";
import Input from "@/components/input";
import Button from "@/components/button";
import { useNavigate } from "react-router-dom";
import { Link } from "react-router-dom";
import { DASHBOARD_URL } from "@/utils/urls/frontend";
import { FORGOT_PASSWORD_URL } from "@/utils/urls/frontend";
import { useAuth } from "@/actions/calls/auth";
import { LoginDetails } from "@/interfaces/dashboard";
import View from "@/components/view";
import Text from "@/components/text";
import * as Yup from "yup";
import { toast } from "@/components/ui/use-toast";

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const navigate = useNavigate();

  const { loginHandler } = useAuth();

  const [loginData, setLoginData] = useState<LoginDetails>({
    email: "",
    password: "",
  });

  const validationForm = Yup.object({
    email: Yup.string()
      .matches(
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        "Invalid email address"
      )
      .required("Email is required"),
    password: Yup.string()
      .min(6, "Password must be at least 6 characters")
      .matches(
        /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{6,}$/,
        "Must include uppercase, lowercase, number, special character, and be at least 6 characters"
      )
      .required("Password is required"),
  });

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target as HTMLInputElement;
    setLoginData((prev) => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    let loginFormObj: Partial<LoginDetails> = {};

    try {
      await validationForm.validate(loginData, { abortEarly: false });
      setErrors({});
      setIsSubmitting(true);
      for (let [key, value] of formData.entries()) {
        if (typeof value === "string") {
          loginFormObj[key as keyof LoginDetails] = value;
        }
      }
      loginHandler(loginFormObj, (success: boolean, response) => {
        if (success) {
          navigate(DASHBOARD_URL);
          // window.location.reload();
        } else {
          setIsSubmitting(false);
          toast({
            title: "Error!",
            description: response?.error,
            variant: "destructive",
          });
        }
      });
    } catch (error: any) {
      console.error("Validation Error:", error);
      setIsSubmitting(false);
      if (error.inner) {
        const validationErrors: Record<string, string> = {};
        error.inner.forEach((e: any) => {
          validationErrors[e.path] = e.message;
        });
        setErrors(validationErrors);
      }
    }
  };

  return (
    <View className="min-h-screen bg-primary-50 dark:bg-background flex flex-col justify-center items-center p-4">
      {/* Header */}
      <View className="text-center mb-6">
        <Text
          as="h1"
          className="text-primary-600 text-3xl md:text-4xl font-bold"
        >
          {import.meta.env.VITE_HOSPITAL_NAME}
        </Text>
        <Text className="text-text-light mt-1">
          {import.meta.env.VITE_TYPE_OF_APPLICATION}
        </Text>
      </View>

      {/* Login Card */}
      <View className="bg-card dark:bg-card rounded-lg shadow-card w-full max-w-md p-6 md:p-8">
        <Text
          as="h2"
          className="text-2xl font-bold text-center text-text-DEFAULT mb-2 dark:text-white"
        >
          Login
        </Text>
        <Text className="text-text-light text-center mb-6">
          Enter your credentials to access your account
        </Text>

        {/* <p className="text-text-light text-center mb-6">{localStorage.getItem("token")}</p> */}
        <form onSubmit={handleLogin}>
          {/* Email Input */}
          <View className="mb-4">
            <Text
              as="label"
              className="block text-sm font-medium text-text-DEFAULT mb-1 dark:text-white"
            >
              Email
            </Text>
            <Input
              type="text"
              id="email"
              name="email"
              value={loginData.email}
              onChange={handleChange}
              className="w-full p-3 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-300 transition dark:text-white"
              placeholder="Email Address"
              error={errors.email}
            />
          </View>

          {/* Password Input */}
          <View className="mb-4">
            <View className="flex items-center justify-between mb-1">
              <Text
                as="label"
                className="block text-sm font-medium text-text-DEFAULT dark:text-white"
              >
                Password
              </Text>
              <Link
                to={FORGOT_PASSWORD_URL}
                className="text-sm text-primary hover:text-primary-600 transition"
              >
                Forgot password?
              </Link>
            </View>
            <View className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                id="password"
                name="password"
                value={loginData.password}
                onChange={handleChange}
                className="w-full p-3 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-300 transition dark:text-white"
                placeholder="Password"
                error={errors.password}
              />
              <Button
                variant="ghost"
                htmlType="button"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500 hover:text-neutral-700"
                onPress={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path
                      fillRule="evenodd"
                      d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z"
                      clipRule="evenodd"
                    />
                    <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                  </svg>
                )}
              </Button>
            </View>
          </View>

          {/* Remember Me Checkbox */}
          {/* <View className="mb-6">
            <label className="flex items-center cursor-pointer">
              <Input
                type="checkbox"
                checked={rememberMe}
                onChange={() => setRememberMe(!rememberMe)}
                className="h-4 w-4 text-primary border-neutral-300 rounded focus:ring-primary-500"
              />
              <span className="ml-2 text-sm text-text-light">Remember me</span>
            </label>
          </div> */}

          {/* Login Button */}
          <Button htmlType="submit" variant="primary" className="w-full">
            {isSubmitting ? "Logging in..." : "Login"}
          </Button>
        </form>

        {/* Register Link */}
        {/* <div className="text-center mt-6">
          <p className="text-text-light text-sm">
            Don't have an account?{" "}
            <Link
              to={USER_URL}
              className="text-primary font-medium hover:text-primary-600 transition"
            >
              Register
            </Link>
          </p>
        </div> */}
      </View>

      {/* Footer */}
      <View className="mt-8 text-center text-text-lighter text-sm">
        © {new Date().getFullYear()} {import.meta.env.VITE_HOSPITAL_NAME}{" "}
        Hospital Management System. All rights reserved.
      </View>
    </View>
  );
};

export default Login;
