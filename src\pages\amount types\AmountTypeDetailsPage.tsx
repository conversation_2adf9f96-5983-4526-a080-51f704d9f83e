// import AppointmentIndex from ".";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { useNavigate, useParams } from "react-router-dom";
import { useAmountType } from "@/actions/calls/amountType";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { DollarSign, FileText } from "lucide-react";
import Button from "@/components/button";
import { ArrowLeft } from "lucide-react";
// import parseDescription from "@/utils/parseDescription";
import View from "@/components/view";
import Text from "@/components/text";
import BouncingLoader from "@/components/BouncingLoader";
import { clearAmountTypeDetailSlice } from "@/actions/slices/amountType";

const AmountTypeDetailsPage = () => {
  const params = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);

  const { amountTypeDetailHandler, cleanUp } = useAmountType();


  const amountTypeData = useSelector(
    (state: RootState) => state.amountType.amountTypeDetailData
  );

  useEffect(() => {
    if (params?.id) {
      amountTypeDetailHandler(params.id, () => {}, [], (status) => {
        setIsLoading(status === "pending" ? true : status === "failed" ? true : status === "success" && false);
      }
      );
    }
    return () => {
      cleanUp();
      dispatch(clearAmountTypeDetailSlice());
    };
  }, [params?.id]);

  return (
        <View className="min-h-screen p-4">
          <BouncingLoader isLoading={isLoading} />
      <View className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <View className="flex items-center gap-4 mb-6 flex item-center justify-between">
          <View>
            <Text as="h1" weight="font-semibold" className="text-2xl">Amount Type Details</Text>
            {/* <Text as="p" className="text-muted-foreground">Details of Amount Type</Text> */}
          </View>
          <Button variant="outline" className="flex items-center" onPress={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
        </View>

        {/* Amount Type Information Card */}
        <Card>
          <CardHeader>
            {/* <View className="flex items-center justify-between">
              <View>
                <CardTitle className="text-2xl">{amountTypeData?.amount_for}</CardTitle>
                <View className="flex items-center gap-2 mt-2">
                  <Hash className="h-4 w-4 text-muted-foreground" />
                  <Text as="span" className="text-sm text-muted-foreground">Type ID: {amountTypeData?.id}</Text>
                </View>
              </View>
              <Badge style={getStatusColorScheme(amountTypeData?.status)}>
                {amountTypeData?.status || "N/A"}
              </Badge>
            </View> */}
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Information */}
            <View>
              <Text as="h3" weight="font-semibold" className=" mb-3 flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                Basic Information
              </Text>
              <View className="grid md:grid-cols-2 gap-4">
                <View>
                  <Text as="label" weight="font-medium" className="text-sm text-muted-foreground">Amount type</Text>
                  <Text as="p" >{amountTypeData?.amount_for || "N/A"}</Text>
                </View>
                <View>
                  <Text as="label" weight="font-medium" className="text-sm text-muted-foreground">Status</Text>
                  <Text className={`${amountTypeData?.status === "Active" ? "text-green-500" : "text-yellow-500"}`}>{amountTypeData?.status || "N/A"}</Text>
                </View>
              </View>
            </View>

            {/* Description */}
            {amountTypeData?.description && (
              <View className="border-t pt-4">
                <Text as="h3" weight="font-semibold" className="mb-2 flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  Description
                </Text>
                <View dangerouslySetInnerHTML={{
                        __html: amountTypeData?.description || "N/A",
                      }} className="leading-relaxed" ></View>
              </View>
            )}
          </CardContent>
        </Card>
      </View>
    </View>
  )
};

export default AmountTypeDetailsPage;
