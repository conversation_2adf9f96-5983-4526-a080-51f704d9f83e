import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Clock } from "lucide-react";
import View from "@/components/view";
import Text from "@/components/text";
import Button from "@/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { useYogaAsana } from "@/actions/calls/yogaAsana";
import { clearYogaAsanaDetailSlice } from "@/actions/slices/yogaAsana";
import BouncingLoader from "@/components/BouncingLoader";

const YogaAsanaDetail: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { yogaAsanaDetailHandler, cleanUp } = useYogaAsana();
  const [isLoading, setIsLoading] = useState(false);
  const yogasanaDetails = useSelector(
    (state: RootState) => state?.yogaAsana?.yogaAsanaDetailData
  );
  useEffect(() => {
    if (id) {
      yogaAsanaDetailHandler(id, () => {}, [], (status) => {
        setIsLoading(status === "pending" ? true : status === "failed" ? true : status === "success" && false);
      }
      );
    }
    return () => {
      cleanUp();
      dispatch(clearYogaAsanaDetailSlice());
    };
  }, [id]);

  const getDifficultyColor = (level: string) => {
    switch (level.toLowerCase()) {
      case "beginner":
        return "bg-green-100 text-green-800";
      case "intermediate":
        return "bg-yellow-100 text-yellow-800";
      case "advanced":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    return status.toLowerCase() === "active"
      ? "bg-green-100 text-green-800"
      : "bg-gray-100 text-gray-800";
  };

  return (
    <View className="container mx-auto py-8 space-y-6">
      <BouncingLoader isLoading={isLoading} />
      {/* Header Section */}
      <View className="flex justify-between items-center">
        <View>
          <View className="flex items-center mb-2">
            <Text as="h1" weight="font-semibold" className="text-2xl font-bold">
              Asana Details
            </Text>
          </View>
          <Text as="p" className="text-muted-foreground">
            Detailed information about this Asana
          </Text>
        </View>
        <Button
          variant="primary"
          className="mr-4 flex items-center justify-center gap-2"
          onPress={() => navigate(-1)}
        >
          Back to Home
        </Button>
      </View>

      {/* Main Asana Information Card */}
      <Card>
        <CardHeader className="pb-2">
          <View className="flex justify-between items-center">
            <CardTitle className="text-lg">
              {yogasanaDetails?.asana_name}
            </CardTitle>
            <Text
              as="span"
              className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(
                yogasanaDetails?.status || ""
              )}`}
            >
              {yogasanaDetails?.status}
            </Text>
          </View>
        </CardHeader>
        <CardContent>
          <View className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <View>
              <Text as="h3" className="text-md font-semibold mb-2">
                Difficulty Level
              </Text>
              <Text
                as="span"
                className={`px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(
                  yogasanaDetails?.difficulty_level || ""
                )}`}
              >
                {yogasanaDetails?.difficulty_level || "N/A"}
              </Text>
            </View>
            <View>
              <Text as="h3" className="text-md font-semibold mb-2">
                Recommended Duration
              </Text>
              <Text
                as="p"
                className="text-muted-foreground text-sm flex items-center"
              >
                <Clock className="h-4 w-4 mr-1 text-primary" />
                {yogasanaDetails?.recommended_duration} minutes
              </Text>
            </View>
          </View>
        </CardContent>
      </Card>

      {/* Benefits Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Benefits</CardTitle>
        </CardHeader>
        <CardContent>
          <View className="prose prose-sm max-w-none">
            <View
              dangerouslySetInnerHTML={{
                __html: yogasanaDetails?.benefits,
              }}
            />
          </View>
        </CardContent>
      </Card>

      {/* Contraindications Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Contraindications</CardTitle>
        </CardHeader>
        <CardContent>
          <View className="prose prose-sm max-w-none">
            <View
              dangerouslySetInnerHTML={{
                __html: yogasanaDetails?.contraindications,
              }}
            />
          </View>
        </CardContent>
      </Card>
    </View>
  );
};

export default YogaAsanaDetail;
