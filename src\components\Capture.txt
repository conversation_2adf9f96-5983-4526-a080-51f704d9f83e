import React, { useState, useRef, useCallback } from 'react';
import { Camera, RotateCcw, Download, X, Settings, Smartphone, Monitor } from 'lucide-react';

// Custom Webcam component
interface CaptureProps {
  audio?: boolean;
  screenshotFormat?: string;
  videoConstraints?: any;
  className?: string;
  mirrored?: boolean;
  screenshotQuality?: number;
}

const Capture = React.forwardRef<any, CaptureProps>(({
  audio = false,
  screenshotFormat = "image/jpeg",
  videoConstraints = {},
  className = "",
  mirrored = true,
  ...props
}, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);

  React.useEffect(() => {
    const startCamera = async () => {
      try {
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          video: videoConstraints.facingMode ? 
            { facingMode: videoConstraints.facingMode, ...videoConstraints } : 
            { ...videoConstraints },
          audio
        });
        setStream(mediaStream);
        if (videoRef.current) {
          videoRef.current.srcObject = mediaStream;
        }
      } catch (err) {
        console.error('Camera access error:', err);
      }
    };

    startCamera();

    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [videoConstraints, audio]);

  React.useImperativeHandle(ref, () => ({
    getScreenshot: () => {
      if (!videoRef.current) return null;

      const video = videoRef.current as HTMLVideoElement;

      // Simple approach: Use HTML5 Canvas without context manipulation
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Handle mirroring by temporarily applying CSS transform
      const originalTransform = video.style.transform;

      if (mirrored) {
        // Apply mirror transform to video element
        video.style.transform = 'scaleX(-1)';

        // Wait for transform to take effect, then capture
        setTimeout(() => {
          const context = canvas.getContext('2d');
          if (context) {
            context.drawImage(video, 0, 0);
          }

          // Restore original transform
          video.style.transform = originalTransform;
        }, 10);
      } else {
        const context = canvas.getContext('2d');
        if (context) {
          context.drawImage(video, 0, 0);
        }
      }

      return canvas.toDataURL(screenshotFormat);
    }
  }));

  return (
    <video
      ref={videoRef}
      autoPlay
      playsInline
      muted
      className={className}
      style={{ transform: mirrored ? 'scaleX(-1)' : 'none' }}
    />
  );
});

interface WebcamCaptureProps {
  name?: string;
  onChange?: (event: any) => void;
  value?: string | null;
  className?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  quality?: number;
  width?: number;
  height?: number;
}

const WebcamCapture: React.FC<WebcamCaptureProps> = ({
  name = 'webcam-image',
  onChange,
  value,
  className = '',
  placeholder = 'Click to capture image',
  required = false,
  disabled = false,
  quality = 0.92,
  width = 1280,
  height = 720
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [capturedImage, setCapturedImage] = useState(value || null);
  const [facingMode, setFacingMode] = useState('user');
  const [showSettings, setShowSettings] = useState(false);
  const [resolution, setResolution] = useState({ width, height });
  
  const webcamRef = useRef<any>(null);

  const resolutionOptions = [
    { label: 'VGA (640x480)', width: 640, height: 480 },
    { label: 'HD (1280x720)', width: 1280, height: 720 },
    { label: 'Full HD (1920x1080)', width: 1920, height: 1080 },
    { label: '4K (3840x2160)', width: 3840, height: 2160 }
  ];

  const videoConstraints = {
    width: resolution.width,
    height: resolution.height,
    facingMode: facingMode
  };

  const captureImage = useCallback(() => {
    const imageSrc = webcamRef.current?.getScreenshot();
    if (!imageSrc) return;

    setCapturedImage(imageSrc);

    // Convert data URL to File object
    fetch(imageSrc)
      .then(res => res.blob())
      .then(blob => {
        const file = new File([blob], `webcam-capture-${Date.now()}.jpg`, {
          type: 'image/jpeg'
        });

        // Call onChange callback if provided
        if (onChange) {
          onChange({
            target: {
              name,
              value: file,
              files: [file]
            }
          });
        }
      });

    setIsOpen(false);
  }, [name, onChange]);

  const retakePhoto = () => {
    setCapturedImage(null);
    if (onChange) {
      onChange({
        target: {
          name,
          value: null,
          files: []
        }
      });
    }
    setIsOpen(true);
  };

  const downloadImage = () => {
    if (capturedImage) {
      const link = document.createElement('a');
      link.download = `webcam-capture-${Date.now()}.jpg`;
      link.href = capturedImage;
      link.click();
    }
  };

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setShowSettings(false);
  }, []);

  const switchCamera = () => {
    setFacingMode(prev => prev === 'user' ? 'environment' : 'user');
  };

  return (
    <div className={`relative ${className}`}>
      {/* Hidden input for form submission */}
      <input
        type="hidden"
        name={name}
        value={capturedImage ? 'captured' : ''}
        required={required}
      />
      
      {/* Main Input Field */}
      <div
        onClick={() => !disabled && setIsOpen(true)}
        className={`
          group relative overflow-hidden rounded-xl border-2 border-dashed transition-all duration-300 cursor-pointer
          ${capturedImage 
            ? 'border-emerald-400 bg-emerald-50 hover:border-emerald-500 hover:bg-emerald-100' 
            : 'border-gray-300 bg-gray-50 hover:border-blue-400 hover:bg-blue-50'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          min-h-[240px] flex items-center justify-center
        `}
      >
        {capturedImage ? (
          <div className="relative w-full h-full">
            <img
              src={capturedImage}
              alt="Captured"
              className="w-full h-full object-cover rounded-lg"
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-lg flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center">
                <Camera className="w-12 h-12 text-white drop-shadow-lg mx-auto mb-2" />
                <p className="text-white text-sm font-medium drop-shadow">Click to retake</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center p-8">
            <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <Camera className="w-10 h-10 text-white drop-shadow" />
            </div>
            <h3 className="text-gray-800 font-semibold text-lg mb-2">
              {placeholder}
            </h3>
            <p className="text-sm text-gray-500">
              High-quality webcam capture
            </p>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      {capturedImage && (
        <div className="absolute top-4 right-4 flex gap-2">
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              retakePhoto();
            }}
            className="p-2.5 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-200 border border-white/20 hover:scale-105 hover:bg-white"
            title="Retake photo"
          >
            <RotateCcw className="w-4 h-4 text-gray-700" />
          </button>
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              downloadImage();
            }}
            className="p-2.5 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-200 border border-white/20 hover:scale-105 hover:bg-white"
            title="Download image"
          >
            <Download className="w-4 h-4 text-gray-700" />
          </button>
        </div>
      )}

      {/* Camera Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-purple-50">
              <div>
                <h3 className="text-xl font-bold text-gray-900">Camera Capture</h3>
                <p className="text-sm text-gray-600 mt-1">Position yourself and click capture when ready</p>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className={`p-3 rounded-xl transition-all duration-200 ${
                    showSettings 
                      ? 'bg-blue-100 text-blue-600' 
                      : 'hover:bg-gray-100 text-gray-600'
                  }`}
                  title="Settings"
                >
                  <Settings className="w-5 h-5" />
                </button>
                <button
                  onClick={closeModal}
                  className="p-3 rounded-xl hover:bg-red-50 text-gray-600 hover:text-red-600 transition-all duration-200"
                  title="Close"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Settings Panel */}
            {showSettings && (
              <div className="p-6 bg-gradient-to-r from-gray-50 to-blue-50 border-b border-gray-100">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      <Smartphone className="inline w-4 h-4 mr-2" />
                      Camera Source
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        onClick={() => setFacingMode('user')}
                        className={`p-3 rounded-xl border-2 transition-all duration-200 text-sm font-medium ${
                          facingMode === 'user'
                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                            : 'border-gray-200 hover:border-gray-300 text-gray-600'
                        }`}
                      >
                        Front Camera
                      </button>
                      <button
                        onClick={() => setFacingMode('environment')}
                        className={`p-3 rounded-xl border-2 transition-all duration-200 text-sm font-medium ${
                          facingMode === 'environment'
                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                            : 'border-gray-200 hover:border-gray-300 text-gray-600'
                        }`}
                      >
                        Back Camera
                      </button>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      <Monitor className="inline w-4 h-4 mr-2" />
                      Resolution
                    </label>
                    <select
                      value={`${resolution.width}x${resolution.height}`}
                      onChange={(e) => {
                        const [width, height] = e.target.value.split('x').map(Number);
                        setResolution({ width, height });
                      }}
                      className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all bg-white text-gray-700 font-medium"
                    >
                      {resolutionOptions.map((res) => (
                        <option key={res.label} value={`${res.width}x${res.height}`}>
                          {res.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* Video Feed */}
            <div className="relative bg-gradient-to-br from-gray-900 to-black flex items-center justify-center min-h-[400px] p-4">
              <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-black">
                <Capture
                  ref={webcamRef}
                  audio={false}
                  screenshotFormat="image/jpeg"
                  screenshotQuality={quality}
                  videoConstraints={videoConstraints}
                  className="max-w-full max-h-[60vh] object-contain"
                  mirrored={facingMode === 'user'}
                />
                
                {/* Camera Controls Overlay */}
                <div className="absolute top-4 left-4 right-4 flex justify-between items-start">
                  <div className="flex items-center gap-2 bg-black/50 backdrop-blur-sm text-white px-3 py-2 rounded-full text-sm font-medium">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    LIVE
                  </div>
                  <button
                    onClick={switchCamera}
                    className="p-3 bg-black/50 backdrop-blur-sm text-white rounded-full hover:bg-black/70 transition-all duration-200"
                    title="Switch camera"
                  >
                    <RotateCcw className="w-5 h-5" />
                  </button>
                </div>

                {/* Center focus indicator */}
                <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                  <div className="w-32 h-32 border-2 border-white/30 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>

            {/* Controls */}
            <div className="p-6 bg-gradient-to-r from-gray-50 to-blue-50 flex items-center justify-center gap-4">
              <button
                onClick={closeModal}
                className="px-8 py-3 bg-gray-200 text-gray-700 rounded-xl hover:bg-gray-300 transition-all duration-200 font-semibold border-2 border-transparent hover:border-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={captureImage}
                className="px-10 py-3 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white rounded-xl hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-3"
              >
                <Camera className="w-5 h-5" />
                Capture Photo
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Export the WebcamCapture component as default
export default WebcamCapture;

// Also export the Capture component if needed
export { Capture };

