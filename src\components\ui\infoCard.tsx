import React from "react";
import Text from "../text";
import View from "../view";

interface InfoCardProps {
  label: string;
  value: string | React.ReactNode;
  subValue?: string;
  icon?: React.ReactNode;
  isLink?: boolean;
  className?: string;
  style?: React.CSSProperties;
  titleStyle?: string;
  valueStyle?: string;
  subValueStyle?: string;
  iconStyle?: string;
}

const InfoCard: React.FC<InfoCardProps> = ({
  label,
  value,
  subValue = "",
  icon,
  isLink = false,
  className,
  style,
  titleStyle,
  valueStyle,
  subValueStyle,
  iconStyle,
}: InfoCardProps) => (
  <View
    className={`bg-neutral-50 dark:bg-background rounded-lg p-6 shadow-sm border border-border dark:border-none relative ${className}`}
    style={style}
  >
    <View className="flex items-center mb-2">
      <Text
        as="h3"
        className={`text-sm text-muted-light dark:text-muted-foreground ${
          titleStyle ? titleStyle : ""
        }`}
      >
        {label}
      </Text>
    </View>
    <Text
      weight="font-bold"
      className={` ${
        isLink ? "text-primary cursor-pointer hover:underline" : ""
      } ${valueStyle ? valueStyle : ""}`}
    >
      {value}
    </Text>
    {subValue && (
      <Text
        className={`text-sm text-text-lighter ${
          subValueStyle ? subValueStyle : ""
        }`}
      >
        {subValue}
      </Text>
    )}
    {icon && (
      <View
        className={`p-3 rounded-full bg-primary-50 text-primary-500 absolute top-1/2 -translate-y-1/2 ${
          iconStyle ? iconStyle : ""
        }`}
        style={{ right: "1.5rem" }}
      >
        {icon}
      </View>
    )}
  </View>
);

export default InfoCard;
