import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
//*++
import { fileURLToPath } from "url";
import { dirname } from "path";

const __dirname = dirname(fileURLToPath(import.meta.url));
//++
// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"), // This sets '@' to refer to the 'src' folder
    },
  },
});
