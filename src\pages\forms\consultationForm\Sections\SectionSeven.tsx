import React, { useEffect } from "react";
import View from "@/components/view";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import useForm from "@/utils/custom-hooks/use-form";
import SingleSelector from "@/components/SingleSelector";
import { Consultation } from "@/interfaces/consultation";
// import { fistulaPositionTypes, fistulaSphincterTypes } from "../consultationFormOptions";
import Input from "@/components/input";
import CollapsibleContainer from "@/components/CollapsibleContainer";
import { useFistula } from "@/actions/calls/fistula";

interface SectionSevenProps {}

const SectionSeven: React.FC<SectionSevenProps> = ({}) => {
  const consultationDetail = useSelector(
    (state: RootState) => state.consultation.consultationDetailData
  );
  const fistulaDropDownList = useSelector(
    (state: RootState) => state.fistula.fistulaDropdownData
  );

  const consultationDetailData = {
    ...consultationDetail?.consultations,
    ...consultationDetail?.proctologyOrNonProctology,
  };

  const { fistulaDropdownHandler } = useFistula();

  const { values, handleChange, onSetHandler } = useForm<Consultation | null>(
    consultationDetailData
  );

  useEffect(() => {
    fistulaDropdownHandler(() => {});
  }, []);

  return (
    <React.Fragment>
      <CollapsibleContainer title="Fistula" variant="minimal">
        <View className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <View>
            <SingleSelector
              id="type_of_fistula_position"
              label="Type of Fistula Position"
              name="type_of_fistula_position"
              value={values?.type_of_fistula_position || ""}
              placeholder="Select Fistula Position"
              onChange={(value) => {
                onSetHandler("type_of_fistula_position", value);
              }}
              options={fistulaDropDownList
                ?.filter((x) => x?.sub_fistula_name === "position")
                .map((x) => ({
                  label: x.fistula_name,
                  value: x.fistula_name,
                }))}
            />
          </View>
          <View>
            <SingleSelector
              id="type_of_fistula_sphincter"
              label="Type of Fistula Sphincter"
              name="type_of_fistula_sphincter"
              value={values?.type_of_fistula_sphincter || ""}
              placeholder="Select Fistula Sphincter"
              onChange={(value) => {
                onSetHandler("type_of_fistula_sphincter", value);
              }}
              options={fistulaDropDownList
                ?.filter((x) => x?.sub_fistula_name === "sphincter")
                .map((x) => ({
                  label: x.fistula_name,
                  value: x.fistula_name,
                }))}
              // options={fistulaSphincterTypes}
            />
          </View>
          <View>
            <Input
              id="no_of_tracks_in_one_fistula"
              name="no_of_tracks_in_one_fistula"
              label="No of Tracks in one Fistula"
              value={values?.no_of_tracks_in_one_fistula || ""}
              onChange={handleChange}
              placeholder="Enter No of Tracks in one Fistula"
            />
          </View>
          <View>
            <Input
              id="no_of_fistula"
              name="no_of_fistula"
              label="No of Fistula"
              value={values?.no_of_fistula || ""}
              onChange={handleChange}
              placeholder="Enter No of Fistula"
            />
          </View>
          <View className="col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              id="posterior_fistulous_angle"
              name="posterior_fistulous_angle"
              label="Posterior Fistulous Angle"
              value={values?.posterior_fistulous_angle || ""}
              onChange={handleChange}
              placeholder="Enter Posterior Fistulous Angle"
            />
            <Input
              label="Posterior Fistulous Angle Suffixed with"
              value={"degree"}
              readOnly={true}
              placeholder="Enter Anterior Fistulous Angle"
            />
          </View>
          <View>
            <Input
              id="sonologist"
              name="sonologist"
              label="Sonologist"
              value={values?.sonologist || ""}
              onChange={handleChange}
              placeholder="Enter Sonologist"
            />
          </View>
        </View>
      </CollapsibleContainer>
    </React.Fragment>
  );
};
export default SectionSeven;
