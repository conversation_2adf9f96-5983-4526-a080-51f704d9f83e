 import { GenericStatus } from "../index";

export type Status =
  | GenericStatus.ACTIVE
  | GenericStatus.INACTIVE;

export enum FistulaType {
  POSITION = "position",
  SPHINCTER = "sphincter",
}

export interface Fistula {
  id?: number;
  fistula_name: string;
  description?: string;
  sub_fistula_name?: FistulaType;
  department_type: string;
  is_active: Status;
}

export interface FistulaState {
  fistulaDetailData: any;
  fistulaListData: Fistula[] | any;
  fistulaDropdownData: any[];
}