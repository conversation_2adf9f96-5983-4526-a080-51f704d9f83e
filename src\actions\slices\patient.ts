import { AuthPayload } from "@/interfaces/slices/auth";
import { PatientState } from "@/interfaces/slices/patient";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const initialState: PatientState = {
  patientDetailData: {},
  patientListData: [],
  userCompleteObj: null,
};

const patientSlice = createSlice({
  name: "patient",
  initialState,
  reducers: {
    patientDetailSlice: (
      state: PatientState,
      action: PayloadAction<AuthPayload>
    ) => {
      state.patientDetailData = action.payload?.data;
    },

    patientListSlice: (
      state: PatientState,
      action: PayloadAction<AuthPayload>
    ) => {
      // state.patientListData = action.payload?.data;
      state.userCompleteObj = action?.payload;
    },

    clearUserDetailsSlice: (state) => {
      state.patientDetailData = null;
    },

    // deletePatientSuccess: (state, action: PayloadAction<string>) => {
    //   state.patientListData = state.patientListData.filter(
    //     (patient) => patient?.id !== action.payload
    //   );
    //   if (state.patientDetailData?.id === action.payload) {
    //     state.patientDetailData = null;
    //   }
      // state.loading = false;
//     },
  },
});


export const {
  patientDetailSlice,
  patientListSlice,
  clearUserDetailsSlice,
  // deletePatientSuccess,
} = patientSlice.actions;

export default patientSlice.reducer;
